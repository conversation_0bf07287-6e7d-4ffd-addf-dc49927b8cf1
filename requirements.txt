# Discord Music Bot Requirements

# Core Discord library
discord.py>=2.3.2

# Audio/Video processing
yt-dlp>=2024.8.6
ffmpeg-python>=0.2.0

# Spotify integration
spotipy>=2.24.0

# HTTP requests
aiohttp>=3.9.0
requests>=2.31.0

# Async support
asyncio-mqtt>=0.16.2

# Data handling
python-dotenv>=1.0.0

# Optional: For better performance
PyNaCl>=1.5.0

# Optional: For web interface (if implementing)
flask>=3.0.0
websockets>=12.0

# Development dependencies (optional)
pytest>=8.0.0
black>=24.0.0