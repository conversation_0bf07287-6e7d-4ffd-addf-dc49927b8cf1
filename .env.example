# Discord Music Bot Environment Variables
# Reference: Discord bot token
DISCORD_TOKEN=your_discord_bot_token_here
# Reference: Spotify API credentials
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
# Bot config
COMMAND_PREFIX=!
BOT_ACTIVITY=Listening to music
MAX_QUEUE_SIZE=100
# Audio config
DEFAULT_VOLUME=50
MAX_VOLUME=100
AUDIO_BITRATE=128
# Advanced config
ENABLE_WEB_INTERFACE=false
WEB_PORT=8080
LOG_LEVEL=INFO