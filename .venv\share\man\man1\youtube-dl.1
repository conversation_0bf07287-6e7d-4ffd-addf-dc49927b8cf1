.\" Automatically generated by Pandoc 2.1.3
.\"
.TH "YOUTUBE\-DL" "1" "" "" ""
.hy
.SH NAME
.PP
youtube\-dl \- download videos from youtube.com or other video platforms
.SH SYNOPSIS
.PP
\f[B]youtube\-dl\f[] [OPTIONS] URL [URL...]
.SH DESCRIPTION
.PP
\f[B]youtube\-dl\f[] is a command\-line program to download videos from
YouTube.com and a few more sites.
It requires the Python interpreter, version 2.6, 2.7, or 3.2+, and it is
not platform specific.
It should work on your Unix box, on Windows or on macOS.
It is released to the public domain, which means you can modify it,
redistribute it or use it however you like.
.SH OPTIONS
.TP
.B \-h, \-\-help
Print this help text and exit
.RS
.RE
.TP
.B \-\-version
Print program version and exit
.RS
.RE
.TP
.B \-U, \-\-update
Update this program to latest version.
Make sure that you have sufficient permissions (run with sudo if needed)
.RS
.RE
.TP
.B \-i, \-\-ignore\-errors
Continue on download errors, for example to skip unavailable videos in a
playlist
.RS
.RE
.TP
.B \-\-abort\-on\-error
Abort downloading of further videos (in the playlist or the command
line) if an error occurs
.RS
.RE
.TP
.B \-\-dump\-user\-agent
Display the current browser identification
.RS
.RE
.TP
.B \-\-list\-extractors
List all supported extractors
.RS
.RE
.TP
.B \-\-extractor\-descriptions
Output descriptions of all supported extractors
.RS
.RE
.TP
.B \-\-force\-generic\-extractor
Force extraction to use the generic extractor
.RS
.RE
.TP
.B \-\-default\-search \f[I]PREFIX\f[]
Use this prefix for unqualified URLs.
For example "gvsearch2:" downloads two videos from google videos for
youtube\- dl "large apple".
Use the value "auto" to let youtube\-dl guess ("auto_warning" to emit a
warning when guessing).
"error" just throws an error.
The default value "fixup_error" repairs broken URLs, but emits an error
if this is not possible instead of searching.
.RS
.RE
.TP
.B \-\-ignore\-config
Do not read configuration files.
When given in the global configuration file /etc/youtube\-dl.conf: Do
not read the user configuration in ~/.config/youtube\-dl/config
(%APPDATA%/youtube\-dl/config.txt on Windows)
.RS
.RE
.TP
.B \-\-config\-location \f[I]PATH\f[]
Location of the configuration file; either the path to the config or its
containing directory.
.RS
.RE
.TP
.B \-\-flat\-playlist
Do not extract the videos of a playlist, only list them.
.RS
.RE
.TP
.B \-\-mark\-watched
Mark videos watched (YouTube only)
.RS
.RE
.TP
.B \-\-no\-mark\-watched
Do not mark videos watched (YouTube only)
.RS
.RE
.TP
.B \-\-no\-color
Do not emit color codes in output
.RS
.RE
.SS Network Options:
.TP
.B \-\-proxy \f[I]URL\f[]
Use the specified HTTP/HTTPS/SOCKS proxy.
To enable SOCKS proxy, specify a proper scheme.
For example socks5://127.0.0.1:1080/.
Pass in an empty string (\-\-proxy "") for direct connection
.RS
.RE
.TP
.B \-\-socket\-timeout \f[I]SECONDS\f[]
Time to wait before giving up, in seconds
.RS
.RE
.TP
.B \-\-source\-address \f[I]IP\f[]
Client\-side IP address to bind to
.RS
.RE
.TP
.B \-4, \-\-force\-ipv4
Make all connections via IPv4
.RS
.RE
.TP
.B \-6, \-\-force\-ipv6
Make all connections via IPv6
.RS
.RE
.SS Geo Restriction:
.TP
.B \-\-geo\-verification\-proxy \f[I]URL\f[]
Use this proxy to verify the IP address for some geo\-restricted sites.
The default proxy specified by \-\-proxy (or none, if the option is not
present) is used for the actual downloading.
.RS
.RE
.TP
.B \-\-geo\-bypass
Bypass geographic restriction via faking X\-Forwarded\-For HTTP header
.RS
.RE
.TP
.B \-\-no\-geo\-bypass
Do not bypass geographic restriction via faking X\-Forwarded\-For HTTP
header
.RS
.RE
.TP
.B \-\-geo\-bypass\-country \f[I]CODE\f[]
Force bypass geographic restriction with explicitly provided two\-letter
ISO 3166\-2 country code
.RS
.RE
.TP
.B \-\-geo\-bypass\-ip\-block \f[I]IP_BLOCK\f[]
Force bypass geographic restriction with explicitly provided IP block in
CIDR notation
.RS
.RE
.SS Video Selection:
.TP
.B \-\-playlist\-start \f[I]NUMBER\f[]
Playlist video to start at (default is 1)
.RS
.RE
.TP
.B \-\-playlist\-end \f[I]NUMBER\f[]
Playlist video to end at (default is last)
.RS
.RE
.TP
.B \-\-playlist\-items \f[I]ITEM_SPEC\f[]
Playlist video items to download.
Specify indices of the videos in the playlist separated by commas like:
"\-\- playlist\-items 1,2,5,8" if you want to download videos indexed 1,
2, 5, 8 in the playlist.
You can specify range: " \-\-playlist\-items 1\-3,7,10\-13", it will
download the videos at index 1, 2, 3, 7, 10, 11, 12 and 13.
.RS
.RE
.TP
.B \-\-match\-title \f[I]REGEX\f[]
Download only matching titles (regex or caseless sub\-string)
.RS
.RE
.TP
.B \-\-reject\-title \f[I]REGEX\f[]
Skip download for matching titles (regex or caseless sub\-string)
.RS
.RE
.TP
.B \-\-max\-downloads \f[I]NUMBER\f[]
Abort after downloading NUMBER files
.RS
.RE
.TP
.B \-\-min\-filesize \f[I]SIZE\f[]
Do not download any videos smaller than SIZE (e.g.
50k or 44.6m)
.RS
.RE
.TP
.B \-\-max\-filesize \f[I]SIZE\f[]
Do not download any videos larger than SIZE (e.g.
50k or 44.6m)
.RS
.RE
.TP
.B \-\-date \f[I]DATE\f[]
Download only videos uploaded in this date
.RS
.RE
.TP
.B \-\-datebefore \f[I]DATE\f[]
Download only videos uploaded on or before this date (i.e.
inclusive)
.RS
.RE
.TP
.B \-\-dateafter \f[I]DATE\f[]
Download only videos uploaded on or after this date (i.e.
inclusive)
.RS
.RE
.TP
.B \-\-min\-views \f[I]COUNT\f[]
Do not download any videos with less than COUNT views
.RS
.RE
.TP
.B \-\-max\-views \f[I]COUNT\f[]
Do not download any videos with more than COUNT views
.RS
.RE
.TP
.B \-\-match\-filter \f[I]FILTER\f[]
Generic video filter.
Specify any key (see the "OUTPUT TEMPLATE" for a list of available keys)
to match if the key is present, !key to check if the key is not present,
key > NUMBER (like "comment_count > 12", also works with >=, <, <=, !=,
=) to compare against a number, key = \[aq]LITERAL\[aq] (like "uploader
= \[aq]Mike Smith\[aq]", also works with !=) to match against a string
literal and & to require multiple matches.
Values which are not known are excluded unless you put a question mark
(?) after the operator.
For example, to only match videos that have been liked more than 100
times and disliked less than 50 times (or the dislike functionality is
not available at the given service), but who also have a description,
use \-\-match\-filter "like_count > 100 & dislike_count <?
50 & description" .
.RS
.RE
.TP
.B \-\-no\-playlist
Download only the video, if the URL refers to a video and a playlist.
.RS
.RE
.TP
.B \-\-yes\-playlist
Download the playlist, if the URL refers to a video and a playlist.
.RS
.RE
.TP
.B \-\-age\-limit \f[I]YEARS\f[]
Download only videos suitable for the given age
.RS
.RE
.TP
.B \-\-download\-archive \f[I]FILE\f[]
Download only videos not listed in the archive file.
Record the IDs of all downloaded videos in it.
.RS
.RE
.TP
.B \-\-include\-ads
Download advertisements as well (experimental)
.RS
.RE
.SS Download Options:
.TP
.B \-r, \-\-limit\-rate \f[I]RATE\f[]
Maximum download rate in bytes per second (e.g.
50K or 4.2M)
.RS
.RE
.TP
.B \-R, \-\-retries \f[I]RETRIES\f[]
Number of retries (default is 10), or "infinite".
.RS
.RE
.TP
.B \-\-fragment\-retries \f[I]RETRIES\f[]
Number of retries for a fragment (default is 10), or "infinite" (DASH,
hlsnative and ISM)
.RS
.RE
.TP
.B \-\-skip\-unavailable\-fragments
Skip unavailable fragments (DASH, hlsnative and ISM)
.RS
.RE
.TP
.B \-\-abort\-on\-unavailable\-fragment
Abort downloading when some fragment is not available
.RS
.RE
.TP
.B \-\-keep\-fragments
Keep downloaded fragments on disk after downloading is finished;
fragments are erased by default
.RS
.RE
.TP
.B \-\-buffer\-size \f[I]SIZE\f[]
Size of download buffer (e.g.
1024 or 16K) (default is 1024)
.RS
.RE
.TP
.B \-\-no\-resize\-buffer
Do not automatically adjust the buffer size.
By default, the buffer size is automatically resized from an initial
value of SIZE.
.RS
.RE
.TP
.B \-\-http\-chunk\-size \f[I]SIZE\f[]
Size of a chunk for chunk\-based HTTP downloading (e.g.
10485760 or 10M) (default is disabled).
May be useful for bypassing bandwidth throttling imposed by a webserver
(experimental)
.RS
.RE
.TP
.B \-\-playlist\-reverse
Download playlist videos in reverse order
.RS
.RE
.TP
.B \-\-playlist\-random
Download playlist videos in random order
.RS
.RE
.TP
.B \-\-xattr\-set\-filesize
Set file xattribute ytdl.filesize with expected file size
.RS
.RE
.TP
.B \-\-hls\-prefer\-native
Use the native HLS downloader instead of ffmpeg
.RS
.RE
.TP
.B \-\-hls\-prefer\-ffmpeg
Use ffmpeg instead of the native HLS downloader
.RS
.RE
.TP
.B \-\-hls\-use\-mpegts
Use the mpegts container for HLS videos, allowing to play the video
while downloading (some players may not be able to play it)
.RS
.RE
.TP
.B \-\-external\-downloader \f[I]COMMAND\f[]
Use the specified external downloader.
Currently supports aria2c,avconv,axel,c url,ffmpeg,httpie,wget
.RS
.RE
.TP
.B \-\-external\-downloader\-args \f[I]ARGS\f[]
Give these arguments to the external downloader
.RS
.RE
.SS Filesystem Options:
.TP
.B \-a, \-\-batch\-file \f[I]FILE\f[]
File containing URLs to download (\[aq]\-\[aq] for stdin), one URL per
line.
Lines starting with \[aq]#\[aq], \[aq];\[aq] or \[aq]]\[aq] are
considered as comments and ignored.
.RS
.RE
.TP
.B \-\-id
Use only video ID in file name
.RS
.RE
.TP
.B \-o, \-\-output \f[I]TEMPLATE\f[]
Output filename template, see the "OUTPUT TEMPLATE" for all the info
.RS
.RE
.TP
.B \-\-output\-na\-placeholder \f[I]PLACEHOLDER\f[]
Placeholder value for unavailable meta fields in output filename
template (default is "NA")
.RS
.RE
.TP
.B \-\-autonumber\-start \f[I]NUMBER\f[]
Specify the start value for %(autonumber)s (default is 1)
.RS
.RE
.TP
.B \-\-restrict\-filenames
Restrict filenames to only ASCII characters, and avoid "&" and spaces in
filenames
.RS
.RE
.TP
.B \-w, \-\-no\-overwrites
Do not overwrite files
.RS
.RE
.TP
.B \-c, \-\-continue
Force resume of partially downloaded files.
By default, youtube\-dl will resume downloads if possible.
.RS
.RE
.TP
.B \-\-no\-continue
Do not resume partially downloaded files (restart from beginning)
.RS
.RE
.TP
.B \-\-no\-part
Do not use .part files \- write directly into output file
.RS
.RE
.TP
.B \-\-no\-mtime
Do not use the Last\-modified header to set the file modification time
.RS
.RE
.TP
.B \-\-write\-description
Write video description to a .description file
.RS
.RE
.TP
.B \-\-write\-info\-json
Write video metadata to a .info.json file
.RS
.RE
.TP
.B \-\-write\-annotations
Write video annotations to a .annotations.xml file
.RS
.RE
.TP
.B \-\-load\-info\-json \f[I]FILE\f[]
JSON file containing the video information (created with the
"\-\-write\- info\-json" option)
.RS
.RE
.TP
.B \-\-cookies \f[I]FILE\f[]
File to read cookies from and dump cookie jar in
.RS
.RE
.TP
.B \-\-cache\-dir \f[I]DIR\f[]
Location in the filesystem where youtube\-dl can store some downloaded
information permanently.
By default $XDG_CACHE_HOME/youtube\-dl or ~/.cache/youtube\-dl .
At the moment, only YouTube player files (for videos with obfuscated
signatures) are cached, but that may change.
.RS
.RE
.TP
.B \-\-no\-cache\-dir
Disable filesystem caching
.RS
.RE
.TP
.B \-\-rm\-cache\-dir
Delete all filesystem cache files
.RS
.RE
.SS Thumbnail Options:
.TP
.B \-\-write\-thumbnail
Write thumbnail image to disk
.RS
.RE
.TP
.B \-\-write\-all\-thumbnails
Write all thumbnail image formats to disk
.RS
.RE
.TP
.B \-\-list\-thumbnails
Simulate and list all available thumbnail formats
.RS
.RE
.SS Verbosity / Simulation Options:
.TP
.B \-q, \-\-quiet
Activate quiet mode
.RS
.RE
.TP
.B \-\-no\-warnings
Ignore warnings
.RS
.RE
.TP
.B \-s, \-\-simulate
Do not download the video and do not write anything to disk
.RS
.RE
.TP
.B \-\-skip\-download
Do not download the video
.RS
.RE
.TP
.B \-g, \-\-get\-url
Simulate, quiet but print URL
.RS
.RE
.TP
.B \-e, \-\-get\-title
Simulate, quiet but print title
.RS
.RE
.TP
.B \-\-get\-id
Simulate, quiet but print id
.RS
.RE
.TP
.B \-\-get\-thumbnail
Simulate, quiet but print thumbnail URL
.RS
.RE
.TP
.B \-\-get\-description
Simulate, quiet but print video description
.RS
.RE
.TP
.B \-\-get\-duration
Simulate, quiet but print video length
.RS
.RE
.TP
.B \-\-get\-filename
Simulate, quiet but print output filename
.RS
.RE
.TP
.B \-\-get\-format
Simulate, quiet but print output format
.RS
.RE
.TP
.B \-j, \-\-dump\-json
Simulate, quiet but print JSON information.
See the "OUTPUT TEMPLATE" for a description of available keys.
.RS
.RE
.TP
.B \-J, \-\-dump\-single\-json
Simulate, quiet but print JSON information for each command\-line
argument.
If the URL refers to a playlist, dump the whole playlist information in
a single line.
.RS
.RE
.TP
.B \-\-print\-json
Be quiet and print the video information as JSON (video is still being
downloaded).
.RS
.RE
.TP
.B \-\-newline
Output progress bar as new lines
.RS
.RE
.TP
.B \-\-no\-progress
Do not print progress bar
.RS
.RE
.TP
.B \-\-console\-title
Display progress in console titlebar
.RS
.RE
.TP
.B \-v, \-\-verbose
Print various debugging information
.RS
.RE
.TP
.B \-\-dump\-pages
Print downloaded pages encoded using base64 to debug problems (very
verbose)
.RS
.RE
.TP
.B \-\-write\-pages
Write downloaded intermediary pages to files in the current directory to
debug problems
.RS
.RE
.TP
.B \-\-print\-traffic
Display sent and read HTTP traffic
.RS
.RE
.TP
.B \-C, \-\-call\-home
Contact the youtube\-dl server for debugging
.RS
.RE
.TP
.B \-\-no\-call\-home
Do NOT contact the youtube\-dl server for debugging
.RS
.RE
.SS Workarounds:
.TP
.B \-\-encoding \f[I]ENCODING\f[]
Force the specified encoding (experimental)
.RS
.RE
.TP
.B \-\-no\-check\-certificate
Suppress HTTPS certificate validation
.RS
.RE
.TP
.B \-\-prefer\-insecure
Use an unencrypted connection to retrieve information about the video.
(Currently supported only for YouTube)
.RS
.RE
.TP
.B \-\-user\-agent \f[I]UA\f[]
Specify a custom user agent
.RS
.RE
.TP
.B \-\-referer \f[I]URL\f[]
Specify a custom referer, use if the video access is restricted to one
domain
.RS
.RE
.TP
.B \-\-add\-header \f[I]FIELD:VALUE\f[]
Specify a custom HTTP header and its value, separated by a colon
\[aq]:\[aq].
You can use this option multiple times
.RS
.RE
.TP
.B \-\-bidi\-workaround
Work around terminals that lack bidirectional text support.
Requires bidiv or fribidi executable in PATH
.RS
.RE
.TP
.B \-\-sleep\-interval \f[I]SECONDS\f[]
Number of seconds to sleep before each download when used alone or a
lower bound of a range for randomized sleep before each download
(minimum possible number of seconds to sleep) when used along with
\-\-max\-sleep\-interval.
.RS
.RE
.TP
.B \-\-max\-sleep\-interval \f[I]SECONDS\f[]
Upper bound of a range for randomized sleep before each download
(maximum possible number of seconds to sleep).
Must only be used along with \-\-min\- sleep\-interval.
.RS
.RE
.SS Video Format Options:
.TP
.B \-f, \-\-format \f[I]FORMAT\f[]
Video format code, see the "FORMAT SELECTION" for all the info
.RS
.RE
.TP
.B \-\-all\-formats
Download all available video formats
.RS
.RE
.TP
.B \-\-prefer\-free\-formats
Prefer free video formats unless a specific one is requested
.RS
.RE
.TP
.B \-F, \-\-list\-formats
List all available formats of requested videos
.RS
.RE
.TP
.B \-\-youtube\-skip\-dash\-manifest
Do not download the DASH manifests and related data on YouTube videos
.RS
.RE
.TP
.B \-\-merge\-output\-format \f[I]FORMAT\f[]
If a merge is required (e.g.
bestvideo+bestaudio), output to given container format.
One of mkv, mp4, ogg, webm, flv.
Ignored if no merge is required
.RS
.RE
.SS Subtitle Options:
.TP
.B \-\-write\-sub
Write subtitle file
.RS
.RE
.TP
.B \-\-write\-auto\-sub
Write automatically generated subtitle file (YouTube only)
.RS
.RE
.TP
.B \-\-all\-subs
Download all the available subtitles of the video
.RS
.RE
.TP
.B \-\-list\-subs
List all available subtitles for the video
.RS
.RE
.TP
.B \-\-sub\-format \f[I]FORMAT\f[]
Subtitle format, accepts formats preference, for example: "srt" or
"ass/srt/best"
.RS
.RE
.TP
.B \-\-sub\-lang \f[I]LANGS\f[]
Languages of the subtitles to download (optional) separated by commas,
use \-\-list\-subs for available language tags
.RS
.RE
.SS Authentication Options:
.TP
.B \-u, \-\-username \f[I]USERNAME\f[]
Login with this account ID
.RS
.RE
.TP
.B \-p, \-\-password \f[I]PASSWORD\f[]
Account password.
If this option is left out, youtube\-dl will ask interactively.
.RS
.RE
.TP
.B \-2, \-\-twofactor \f[I]TWOFACTOR\f[]
Two\-factor authentication code
.RS
.RE
.TP
.B \-n, \-\-netrc
Use .netrc authentication data
.RS
.RE
.TP
.B \-\-video\-password \f[I]PASSWORD\f[]
Video password (vimeo, youku)
.RS
.RE
.SS Adobe Pass Options:
.TP
.B \-\-ap\-mso \f[I]MSO\f[]
Adobe Pass multiple\-system operator (TV provider) identifier, use
\-\-ap\-list\-mso for a list of available MSOs
.RS
.RE
.TP
.B \-\-ap\-username \f[I]USERNAME\f[]
Multiple\-system operator account login
.RS
.RE
.TP
.B \-\-ap\-password \f[I]PASSWORD\f[]
Multiple\-system operator account password.
If this option is left out, youtube\-dl will ask interactively.
.RS
.RE
.TP
.B \-\-ap\-list\-mso
List all supported multiple\-system operators
.RS
.RE
.SS Post\-processing Options:
.TP
.B \-x, \-\-extract\-audio
Convert video files to audio\-only files (requires ffmpeg/avconv and
ffprobe/avprobe)
.RS
.RE
.TP
.B \-\-audio\-format \f[I]FORMAT\f[]
Specify audio format: "best", "aac", "flac", "mp3", "m4a", "opus",
"vorbis", or "wav"; "best" by default; No effect without \-x
.RS
.RE
.TP
.B \-\-audio\-quality \f[I]QUALITY\f[]
Specify ffmpeg/avconv audio quality, insert a value between 0 (better)
and 9 (worse) for VBR or a specific bitrate like 128K (default 5)
.RS
.RE
.TP
.B \-\-recode\-video \f[I]FORMAT\f[]
Encode the video to another format if necessary (currently supported:
mp4|flv|ogg|webm|mkv|avi)
.RS
.RE
.TP
.B \-\-postprocessor\-args \f[I]ARGS\f[]
Give these arguments to the postprocessor
.RS
.RE
.TP
.B \-k, \-\-keep\-video
Keep the video file on disk after the post\-processing; the video is
erased by default
.RS
.RE
.TP
.B \-\-no\-post\-overwrites
Do not overwrite post\-processed files; the post\-processed files are
overwritten by default
.RS
.RE
.TP
.B \-\-embed\-subs
Embed subtitles in the video (only for mp4, webm and mkv videos)
.RS
.RE
.TP
.B \-\-embed\-thumbnail
Embed thumbnail in the audio as cover art
.RS
.RE
.TP
.B \-\-add\-metadata
Write metadata to the video file
.RS
.RE
.TP
.B \-\-metadata\-from\-title \f[I]FORMAT\f[]
Parse additional metadata like song title / artist from the video title.
The format syntax is the same as \-\-output.
Regular expression with named capture groups may also be used.
The parsed parameters replace existing values.
Example: \-\-metadata\-from\-title "%(artist)s \- %(title)s" matches a
title like "Coldplay \- Paradise".
Example (regex): \-\-metadata\-from\-title "(?P.+?) \- (?P
.RS
\&.+)"
.RE
.TP
.B \-\-xattrs
Write metadata to the video file\[aq]s xattrs (using dublin core and xdg
standards)
.RS
.RE
.TP
.B \-\-fixup \f[I]POLICY\f[]
Automatically correct known faults of the file.
One of never (do nothing), warn (only emit a warning), detect_or_warn
(the default; fix file if we can, warn otherwise)
.RS
.RE
.TP
.B \-\-prefer\-avconv
Prefer avconv over ffmpeg for running the postprocessors
.RS
.RE
.TP
.B \-\-prefer\-ffmpeg
Prefer ffmpeg over avconv for running the postprocessors (default)
.RS
.RE
.TP
.B \-\-ffmpeg\-location \f[I]PATH\f[]
Location of the ffmpeg/avconv binary; either the path to the binary or
its containing directory.
.RS
.RE
.TP
.B \-\-exec \f[I]CMD\f[]
Execute a command on the file after downloading and post\-processing,
similar to find\[aq]s \-exec syntax.
Example: \-\-exec \[aq]adb push {} /sdcard/Music/ && rm {}\[aq]
.RS
.RE
.TP
.B \-\-convert\-subs \f[I]FORMAT\f[]
Convert the subtitles to other format (currently supported:
srt|ass|vtt|lrc)
.RS
.RE
.SH CONFIGURATION
.PP
You can configure youtube\-dl by placing any supported command line
option to a configuration file.
On Linux and macOS, the system wide configuration file is located at
\f[C]/etc/youtube\-dl.conf\f[] and the user wide configuration file at
\f[C]~/.config/youtube\-dl/config\f[].
On Windows, the user wide configuration file locations are
\f[C]%APPDATA%\\youtube\-dl\\config.txt\f[] or
\f[C]C:\\Users\\<USER>\ name>\\youtube\-dl.conf\f[].
Note that by default configuration file may not exist so you may need to
create it yourself.
.PP
For example, with the following configuration file youtube\-dl will
always extract the audio, not copy the mtime, use a proxy and save all
videos under \f[C]Movies\f[] directory in your home directory:
.IP
.nf
\f[C]
#\ Lines\ starting\ with\ #\ are\ comments

#\ Always\ extract\ audio
\-x

#\ Do\ not\ copy\ the\ mtime
\-\-no\-mtime

#\ Use\ this\ proxy
\-\-proxy\ 127.0.0.1:3128

#\ Save\ all\ videos\ under\ Movies\ directory\ in\ your\ home\ directory
\-o\ ~/Movies/%(title)s.%(ext)s
\f[]
.fi
.PP
Note that options in configuration file are just the same options aka
switches used in regular command line calls thus there \f[B]must be no
whitespace\f[] after \f[C]\-\f[] or \f[C]\-\-\f[], e.g.
\f[C]\-o\f[] or \f[C]\-\-proxy\f[] but not \f[C]\-\ o\f[] or
\f[C]\-\-\ proxy\f[].
.PP
You can use \f[C]\-\-ignore\-config\f[] if you want to disable the
configuration file for a particular youtube\-dl run.
.PP
You can also use \f[C]\-\-config\-location\f[] if you want to use custom
configuration file for a particular youtube\-dl run.
.SS Authentication with \f[C]\&.netrc\f[] file
.PP
You may also want to configure automatic credentials storage for
extractors that support authentication (by providing login and password
with \f[C]\-\-username\f[] and \f[C]\-\-password\f[]) in order not to
pass credentials as command line arguments on every youtube\-dl
execution and prevent tracking plain text passwords in the shell command
history.
You can achieve this using a \f[C]\&.netrc\f[]
file (https://stackoverflow.com/tags/.netrc/info) on a per extractor
basis.
For that you will need to create a \f[C]\&.netrc\f[] file in your
\f[C]$HOME\f[] and restrict permissions to read/write by only you:
.IP
.nf
\f[C]
touch\ $HOME/.netrc
chmod\ a\-rwx,u+rw\ $HOME/.netrc
\f[]
.fi
.PP
After that you can add credentials for an extractor in the following
format, where \f[I]extractor\f[] is the name of the extractor in
lowercase:
.IP
.nf
\f[C]
machine\ <extractor>\ login\ <login>\ password\ <password>
\f[]
.fi
.PP
For example:
.IP
.nf
\f[C]
machine\ youtube\ login\ myaccount\@gmail.com\ password\ my_youtube_password
machine\ twitch\ login\ my_twitch_account_name\ password\ my_twitch_password
\f[]
.fi
.PP
To activate authentication with the \f[C]\&.netrc\f[] file you should
pass \f[C]\-\-netrc\f[] to youtube\-dl or place it in the configuration
file.
.PP
On Windows you may also need to setup the \f[C]%HOME%\f[] environment
variable manually.
For example:
.IP
.nf
\f[C]
set\ HOME=%USERPROFILE%
\f[]
.fi
.SH OUTPUT TEMPLATE
.PP
The \f[C]\-o\f[] option allows users to indicate a template for the
output file names.
.PP
\f[B]tl;dr:\f[] navigate me to examples.
.PP
The basic usage is not to set any template arguments when downloading a
single file, like in
\f[C]youtube\-dl\ \-o\ funny_video.flv\ "https://some/video"\f[].
However, it may contain special sequences that will be replaced when
downloading each video.
The special sequences may be formatted according to python string
formatting
operations (https://docs.python.org/2/library/stdtypes.html#string-formatting).
For example, \f[C]%(NAME)s\f[] or \f[C]%(NAME)05d\f[].
To clarify, that is a percent symbol followed by a name in parentheses,
followed by formatting operations.
Allowed names along with sequence type are:
.IP \[bu] 2
\f[C]id\f[] (string): Video identifier
.IP \[bu] 2
\f[C]title\f[] (string): Video title
.IP \[bu] 2
\f[C]url\f[] (string): Video URL
.IP \[bu] 2
\f[C]ext\f[] (string): Video filename extension
.IP \[bu] 2
\f[C]alt_title\f[] (string): A secondary title of the video
.IP \[bu] 2
\f[C]display_id\f[] (string): An alternative identifier for the video
.IP \[bu] 2
\f[C]uploader\f[] (string): Full name of the video uploader
.IP \[bu] 2
\f[C]license\f[] (string): License name the video is licensed under
.IP \[bu] 2
\f[C]creator\f[] (string): The creator of the video
.IP \[bu] 2
\f[C]release_date\f[] (string): The date (YYYYMMDD) when the video was
released
.IP \[bu] 2
\f[C]timestamp\f[] (numeric): UNIX timestamp of the moment the video
became available
.IP \[bu] 2
\f[C]upload_date\f[] (string): Video upload date (YYYYMMDD)
.IP \[bu] 2
\f[C]uploader_id\f[] (string): Nickname or id of the video uploader
.IP \[bu] 2
\f[C]channel\f[] (string): Full name of the channel the video is
uploaded on
.IP \[bu] 2
\f[C]channel_id\f[] (string): Id of the channel
.IP \[bu] 2
\f[C]location\f[] (string): Physical location where the video was filmed
.IP \[bu] 2
\f[C]duration\f[] (numeric): Length of the video in seconds
.IP \[bu] 2
\f[C]view_count\f[] (numeric): How many users have watched the video on
the platform
.IP \[bu] 2
\f[C]like_count\f[] (numeric): Number of positive ratings of the video
.IP \[bu] 2
\f[C]dislike_count\f[] (numeric): Number of negative ratings of the
video
.IP \[bu] 2
\f[C]repost_count\f[] (numeric): Number of reposts of the video
.IP \[bu] 2
\f[C]average_rating\f[] (numeric): Average rating give by users, the
scale used depends on the webpage
.IP \[bu] 2
\f[C]comment_count\f[] (numeric): Number of comments on the video
.IP \[bu] 2
\f[C]age_limit\f[] (numeric): Age restriction for the video (years)
.IP \[bu] 2
\f[C]is_live\f[] (boolean): Whether this video is a live stream or a
fixed\-length video
.IP \[bu] 2
\f[C]start_time\f[] (numeric): Time in seconds where the reproduction
should start, as specified in the URL
.IP \[bu] 2
\f[C]end_time\f[] (numeric): Time in seconds where the reproduction
should end, as specified in the URL
.IP \[bu] 2
\f[C]format\f[] (string): A human\-readable description of the format
.IP \[bu] 2
\f[C]format_id\f[] (string): Format code specified by
\f[C]\-\-format\f[]
.IP \[bu] 2
\f[C]format_note\f[] (string): Additional info about the format
.IP \[bu] 2
\f[C]width\f[] (numeric): Width of the video
.IP \[bu] 2
\f[C]height\f[] (numeric): Height of the video
.IP \[bu] 2
\f[C]resolution\f[] (string): Textual description of width and height
.IP \[bu] 2
\f[C]tbr\f[] (numeric): Average bitrate of audio and video in KBit/s
.IP \[bu] 2
\f[C]abr\f[] (numeric): Average audio bitrate in KBit/s
.IP \[bu] 2
\f[C]acodec\f[] (string): Name of the audio codec in use
.IP \[bu] 2
\f[C]asr\f[] (numeric): Audio sampling rate in Hertz
.IP \[bu] 2
\f[C]vbr\f[] (numeric): Average video bitrate in KBit/s
.IP \[bu] 2
\f[C]fps\f[] (numeric): Frame rate
.IP \[bu] 2
\f[C]vcodec\f[] (string): Name of the video codec in use
.IP \[bu] 2
\f[C]container\f[] (string): Name of the container format
.IP \[bu] 2
\f[C]filesize\f[] (numeric): The number of bytes, if known in advance
.IP \[bu] 2
\f[C]filesize_approx\f[] (numeric): An estimate for the number of bytes
.IP \[bu] 2
\f[C]protocol\f[] (string): The protocol that will be used for the
actual download
.IP \[bu] 2
\f[C]extractor\f[] (string): Name of the extractor
.IP \[bu] 2
\f[C]extractor_key\f[] (string): Key name of the extractor
.IP \[bu] 2
\f[C]epoch\f[] (numeric): Unix epoch when creating the file
.IP \[bu] 2
\f[C]autonumber\f[] (numeric): Number that will be increased with each
download, starting at \f[C]\-\-autonumber\-start\f[]
.IP \[bu] 2
\f[C]playlist\f[] (string): Name or id of the playlist that contains the
video
.IP \[bu] 2
\f[C]playlist_index\f[] (numeric): Index of the video in the playlist
padded with leading zeros according to the total length of the playlist
.IP \[bu] 2
\f[C]playlist_id\f[] (string): Playlist identifier
.IP \[bu] 2
\f[C]playlist_title\f[] (string): Playlist title
.IP \[bu] 2
\f[C]playlist_uploader\f[] (string): Full name of the playlist uploader
.IP \[bu] 2
\f[C]playlist_uploader_id\f[] (string): Nickname or id of the playlist
uploader
.PP
Available for the video that belongs to some logical chapter or section:
.IP \[bu] 2
\f[C]chapter\f[] (string): Name or title of the chapter the video
belongs to
.IP \[bu] 2
\f[C]chapter_number\f[] (numeric): Number of the chapter the video
belongs to
.IP \[bu] 2
\f[C]chapter_id\f[] (string): Id of the chapter the video belongs to
.PP
Available for the video that is an episode of some series or programme:
.IP \[bu] 2
\f[C]series\f[] (string): Title of the series or programme the video
episode belongs to
.IP \[bu] 2
\f[C]season\f[] (string): Title of the season the video episode belongs
to
.IP \[bu] 2
\f[C]season_number\f[] (numeric): Number of the season the video episode
belongs to
.IP \[bu] 2
\f[C]season_id\f[] (string): Id of the season the video episode belongs
to
.IP \[bu] 2
\f[C]episode\f[] (string): Title of the video episode
.IP \[bu] 2
\f[C]episode_number\f[] (numeric): Number of the video episode within a
season
.IP \[bu] 2
\f[C]episode_id\f[] (string): Id of the video episode
.PP
Available for the media that is a track or a part of a music album:
.IP \[bu] 2
\f[C]track\f[] (string): Title of the track
.IP \[bu] 2
\f[C]track_number\f[] (numeric): Number of the track within an album or
a disc
.IP \[bu] 2
\f[C]track_id\f[] (string): Id of the track
.IP \[bu] 2
\f[C]artist\f[] (string): Artist(s) of the track
.IP \[bu] 2
\f[C]genre\f[] (string): Genre(s) of the track
.IP \[bu] 2
\f[C]album\f[] (string): Title of the album the track belongs to
.IP \[bu] 2
\f[C]album_type\f[] (string): Type of the album
.IP \[bu] 2
\f[C]album_artist\f[] (string): List of all artists appeared on the
album
.IP \[bu] 2
\f[C]disc_number\f[] (numeric): Number of the disc or other physical
medium the track belongs to
.IP \[bu] 2
\f[C]release_year\f[] (numeric): Year (YYYY) when the album was released
.PP
Each aforementioned sequence when referenced in an output template will
be replaced by the actual value corresponding to the sequence name.
Note that some of the sequences are not guaranteed to be present since
they depend on the metadata obtained by a particular extractor.
Such sequences will be replaced with placeholder value provided with
\f[C]\-\-output\-na\-placeholder\f[] (\f[C]NA\f[] by default).
.PP
For example for \f[C]\-o\ %(title)s\-%(id)s.%(ext)s\f[] and an mp4 video
with title \f[C]youtube\-dl\ test\ video\f[] and id
\f[C]BaW_jenozKcj\f[], this will result in a
\f[C]youtube\-dl\ test\ video\-BaW_jenozKcj.mp4\f[] file created in the
current directory.
.PP
For numeric sequences you can use numeric related formatting, for
example, \f[C]%(view_count)05d\f[] will result in a string with view
count padded with zeros up to 5 characters, like in \f[C]00042\f[].
.PP
Output templates can also contain arbitrary hierarchical path, e.g.
\f[C]\-o\ \[aq]%(playlist)s/%(playlist_index)s\ \-\ %(title)s.%(ext)s\[aq]\f[]
which will result in downloading each video in a directory corresponding
to this path template.
Any missing directory will be automatically created for you.
.PP
To use percent literals in an output template use \f[C]%%\f[].
To output to stdout use \f[C]\-o\ \-\f[].
.PP
The current default template is \f[C]%(title)s\-%(id)s.%(ext)s\f[].
.PP
In some cases, you don\[aq]t want special characters such as 中, spaces,
or &, such as when transferring the downloaded filename to a Windows
system or the filename through an 8bit\-unsafe channel.
In these cases, add the \f[C]\-\-restrict\-filenames\f[] flag to get a
shorter title:
.SS Output template and Windows batch files
.PP
If you are using an output template inside a Windows batch file then you
must escape plain percent characters (\f[C]%\f[]) by doubling, so that
\f[C]\-o\ "%(title)s\-%(id)s.%(ext)s"\f[] should become
\f[C]\-o\ "%%(title)s\-%%(id)s.%%(ext)s"\f[].
However you should not touch \f[C]%\f[]\[aq]s that are not plain
characters, e.g.
environment variables for expansion should stay intact:
\f[C]\-o\ "C:\\%HOMEPATH%\\Desktop\\%%(title)s.%%(ext)s"\f[].
.SS Output template examples
.PP
Note that on Windows you may need to use double quotes instead of
single.
.IP
.nf
\f[C]
$\ youtube\-dl\ \-\-get\-filename\ \-o\ \[aq]%(title)s.%(ext)s\[aq]\ BaW_jenozKc
youtube\-dl\ test\ video\ \[aq]\[aq]_ä↭𝕐.mp4\ \ \ \ #\ All\ kinds\ of\ weird\ characters

$\ youtube\-dl\ \-\-get\-filename\ \-o\ \[aq]%(title)s.%(ext)s\[aq]\ BaW_jenozKc\ \-\-restrict\-filenames
youtube\-dl_test_video_.mp4\ \ \ \ \ \ \ \ \ \ #\ A\ simple\ file\ name

#\ Download\ YouTube\ playlist\ videos\ in\ separate\ directory\ indexed\ by\ video\ order\ in\ a\ playlist
$\ youtube\-dl\ \-o\ \[aq]%(playlist)s/%(playlist_index)s\ \-\ %(title)s.%(ext)s\[aq]\ https://www.youtube.com/playlist?list=PLwiyx1dc3P2JR9N8gQaQN_BCvlSlap7re

#\ Download\ all\ playlists\ of\ YouTube\ channel/user\ keeping\ each\ playlist\ in\ separate\ directory:
$\ youtube\-dl\ \-o\ \[aq]%(uploader)s/%(playlist)s/%(playlist_index)s\ \-\ %(title)s.%(ext)s\[aq]\ https://www.youtube.com/user/TheLinuxFoundation/playlists

#\ Download\ Udemy\ course\ keeping\ each\ chapter\ in\ separate\ directory\ under\ MyVideos\ directory\ in\ your\ home
$\ youtube\-dl\ \-u\ user\ \-p\ password\ \-o\ \[aq]~/MyVideos/%(playlist)s/%(chapter_number)s\ \-\ %(chapter)s/%(title)s.%(ext)s\[aq]\ https://www.udemy.com/java\-tutorial/

#\ Download\ entire\ series\ season\ keeping\ each\ series\ and\ each\ season\ in\ separate\ directory\ under\ C:/MyVideos
$\ youtube\-dl\ \-o\ "C:/MyVideos/%(series)s/%(season_number)s\ \-\ %(season)s/%(episode_number)s\ \-\ %(episode)s.%(ext)s"\ https://videomore.ru/kino_v_detalayah/5_sezon/367617

#\ Stream\ the\ video\ being\ downloaded\ to\ stdout
$\ youtube\-dl\ \-o\ \-\ BaW_jenozKc
\f[]
.fi
.SH FORMAT SELECTION
.PP
By default youtube\-dl tries to download the best available quality,
i.e.
if you want the best quality you \f[B]don\[aq]t need\f[] to pass any
special options, youtube\-dl will guess it for you by \f[B]default\f[].
.PP
But sometimes you may want to download in a different format, for
example when you are on a slow or intermittent connection.
The key mechanism for achieving this is so\-called \f[I]format
selection\f[] based on which you can explicitly specify desired format,
select formats based on some criterion or criteria, setup precedence and
much more.
.PP
The general syntax for format selection is \f[C]\-\-format\ FORMAT\f[]
or shorter \f[C]\-f\ FORMAT\f[] where \f[C]FORMAT\f[] is a \f[I]selector
expression\f[], i.e.
an expression that describes format or formats you would like to
download.
.PP
\f[B]tl;dr:\f[] navigate me to examples.
.PP
The simplest case is requesting a specific format, for example with
\f[C]\-f\ 22\f[] you can download the format with format code equal to
22.
You can get the list of available format codes for particular video
using \f[C]\-\-list\-formats\f[] or \f[C]\-F\f[].
Note that these format codes are extractor specific.
.PP
You can also use a file extension (currently \f[C]3gp\f[], \f[C]aac\f[],
\f[C]flv\f[], \f[C]m4a\f[], \f[C]mp3\f[], \f[C]mp4\f[], \f[C]ogg\f[],
\f[C]wav\f[], \f[C]webm\f[] are supported) to download the best quality
format of a particular file extension served as a single file, e.g.
\f[C]\-f\ webm\f[] will download the best quality format with the
\f[C]webm\f[] extension served as a single file.
.PP
You can also use special names to select particular edge case formats:
.IP \[bu] 2
\f[C]best\f[]: Select the best quality format represented by a single
file with video and audio.
.IP \[bu] 2
\f[C]worst\f[]: Select the worst quality format represented by a single
file with video and audio.
.IP \[bu] 2
\f[C]bestvideo\f[]: Select the best quality video\-only format (e.g.
DASH video).
May not be available.
.IP \[bu] 2
\f[C]worstvideo\f[]: Select the worst quality video\-only format.
May not be available.
.IP \[bu] 2
\f[C]bestaudio\f[]: Select the best quality audio only\-format.
May not be available.
.IP \[bu] 2
\f[C]worstaudio\f[]: Select the worst quality audio only\-format.
May not be available.
.PP
For example, to download the worst quality video\-only format you can
use \f[C]\-f\ worstvideo\f[].
.PP
If you want to download multiple videos and they don\[aq]t have the same
formats available, you can specify the order of preference using
slashes.
Note that slash is left\-associative, i.e.
formats on the left hand side are preferred, for example
\f[C]\-f\ 22/17/18\f[] will download format 22 if it\[aq]s available,
otherwise it will download format 17 if it\[aq]s available, otherwise it
will download format 18 if it\[aq]s available, otherwise it will
complain that no suitable formats are available for download.
.PP
If you want to download several formats of the same video use a comma as
a separator, e.g.
\f[C]\-f\ 22,17,18\f[] will download all these three formats, of course
if they are available.
Or a more sophisticated example combined with the precedence feature:
\f[C]\-f\ 136/137/mp4/bestvideo,140/m4a/bestaudio\f[].
.PP
You can also filter the video formats by putting a condition in
brackets, as in \f[C]\-f\ "best[height=720]"\f[] (or
\f[C]\-f\ "[filesize>10M]"\f[]).
.PP
The following numeric meta fields can be used with comparisons
\f[C]<\f[], \f[C]<=\f[], \f[C]>\f[], \f[C]>=\f[], \f[C]=\f[] (equals),
\f[C]!=\f[] (not equals):
.IP \[bu] 2
\f[C]filesize\f[]: The number of bytes, if known in advance
.IP \[bu] 2
\f[C]width\f[]: Width of the video, if known
.IP \[bu] 2
\f[C]height\f[]: Height of the video, if known
.IP \[bu] 2
\f[C]tbr\f[]: Average bitrate of audio and video in KBit/s
.IP \[bu] 2
\f[C]abr\f[]: Average audio bitrate in KBit/s
.IP \[bu] 2
\f[C]vbr\f[]: Average video bitrate in KBit/s
.IP \[bu] 2
\f[C]asr\f[]: Audio sampling rate in Hertz
.IP \[bu] 2
\f[C]fps\f[]: Frame rate
.PP
Also filtering work for comparisons \f[C]=\f[] (equals), \f[C]^=\f[]
(starts with), \f[C]$=\f[] (ends with), \f[C]*=\f[] (contains) and
following string meta fields:
.IP \[bu] 2
\f[C]ext\f[]: File extension
.IP \[bu] 2
\f[C]acodec\f[]: Name of the audio codec in use
.IP \[bu] 2
\f[C]vcodec\f[]: Name of the video codec in use
.IP \[bu] 2
\f[C]container\f[]: Name of the container format
.IP \[bu] 2
\f[C]protocol\f[]: The protocol that will be used for the actual
download, lower\-case (\f[C]http\f[], \f[C]https\f[], \f[C]rtsp\f[],
\f[C]rtmp\f[], \f[C]rtmpe\f[], \f[C]mms\f[], \f[C]f4m\f[], \f[C]ism\f[],
\f[C]http_dash_segments\f[], \f[C]m3u8\f[], or \f[C]m3u8_native\f[])
.IP \[bu] 2
\f[C]format_id\f[]: A short description of the format
.IP \[bu] 2
\f[C]language\f[]: Language code
.PP
Any string comparison may be prefixed with negation \f[C]!\f[] in order
to produce an opposite comparison, e.g.
\f[C]!*=\f[] (does not contain).
.PP
Note that none of the aforementioned meta fields are guaranteed to be
present since this solely depends on the metadata obtained by particular
extractor, i.e.
the metadata offered by the video hoster.
.PP
Formats for which the value is not known are excluded unless you put a
question mark (\f[C]?\f[]) after the operator.
You can combine format filters, so
\f[C]\-f\ "[height\ <=?\ 720][tbr>500]"\f[] selects up to 720p videos
(or videos where the height is not known) with a bitrate of at least 500
KBit/s.
.PP
You can merge the video and audio of two formats into a single file
using \f[C]\-f\ <video\-format>+<audio\-format>\f[] (requires ffmpeg or
avconv installed), for example \f[C]\-f\ bestvideo+bestaudio\f[] will
download the best video\-only format, the best audio\-only format and
mux them together with ffmpeg/avconv.
.PP
Format selectors can also be grouped using parentheses, for example if
you want to download the best mp4 and webm formats with a height lower
than 480 you can use \f[C]\-f\ \[aq](mp4,webm)[height<480]\[aq]\f[].
.PP
Since the end of April 2015 and version 2015.04.26, youtube\-dl uses
\f[C]\-f\ bestvideo+bestaudio/best\f[] as the default format selection
(see #5447 (https://github.com/ytdl-org/youtube-dl/issues/5447),
#5456 (https://github.com/ytdl-org/youtube-dl/issues/5456)).
If ffmpeg or avconv are installed this results in downloading
\f[C]bestvideo\f[] and \f[C]bestaudio\f[] separately and muxing them
together into a single file giving the best overall quality available.
Otherwise it falls back to \f[C]best\f[] and results in downloading the
best available quality served as a single file.
\f[C]best\f[] is also needed for videos that don\[aq]t come from YouTube
because they don\[aq]t provide the audio and video in two different
files.
If you want to only download some DASH formats (for example if you are
not interested in getting videos with a resolution higher than 1080p),
you can add \f[C]\-f\ bestvideo[height<=?1080]+bestaudio/best\f[] to
your configuration file.
Note that if you use youtube\-dl to stream to \f[C]stdout\f[] (and most
likely to pipe it to your media player then), i.e.
you explicitly specify output template as \f[C]\-o\ \-\f[], youtube\-dl
still uses \f[C]\-f\ best\f[] format selection in order to start content
delivery immediately to your player and not to wait until
\f[C]bestvideo\f[] and \f[C]bestaudio\f[] are downloaded and muxed.
.PP
If you want to preserve the old format selection behavior (prior to
youtube\-dl 2015.04.26), i.e.
you want to download the best available quality media served as a single
file, you should explicitly specify your choice with \f[C]\-f\ best\f[].
You may want to add it to the configuration file in order not to type it
every time you run youtube\-dl.
.SS Format selection examples
.PP
Note that on Windows you may need to use double quotes instead of
single.
.IP
.nf
\f[C]
#\ Download\ best\ mp4\ format\ available\ or\ any\ other\ best\ if\ no\ mp4\ available
$\ youtube\-dl\ \-f\ \[aq]bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best\[aq]

#\ Download\ best\ format\ available\ but\ no\ better\ than\ 480p
$\ youtube\-dl\ \-f\ \[aq]bestvideo[height<=480]+bestaudio/best[height<=480]\[aq]

#\ Download\ best\ video\ only\ format\ but\ no\ bigger\ than\ 50\ MB
$\ youtube\-dl\ \-f\ \[aq]best[filesize<50M]\[aq]

#\ Download\ best\ format\ available\ via\ direct\ link\ over\ HTTP/HTTPS\ protocol
$\ youtube\-dl\ \-f\ \[aq](bestvideo+bestaudio/best)[protocol^=http]\[aq]

#\ Download\ the\ best\ video\ format\ and\ the\ best\ audio\ format\ without\ merging\ them
$\ youtube\-dl\ \-f\ \[aq]bestvideo,bestaudio\[aq]\ \-o\ \[aq]%(title)s.f%(format_id)s.%(ext)s\[aq]
\f[]
.fi
.PP
Note that in the last example, an output template is recommended as
bestvideo and bestaudio may have the same file name.
.SH VIDEO SELECTION
.PP
Videos can be filtered by their upload date using the options
\f[C]\-\-date\f[], \f[C]\-\-datebefore\f[] or \f[C]\-\-dateafter\f[].
They accept dates in two formats:
.IP \[bu] 2
Absolute dates: Dates in the format \f[C]YYYYMMDD\f[].
.IP \[bu] 2
Relative dates: Dates in the format
\f[C](now|today)[+\-][0\-9](day|week|month|year)(s)?\f[]
.PP
Examples:
.IP
.nf
\f[C]
#\ Download\ only\ the\ videos\ uploaded\ in\ the\ last\ 6\ months
$\ youtube\-dl\ \-\-dateafter\ now\-6months

#\ Download\ only\ the\ videos\ uploaded\ on\ January\ 1,\ 1970
$\ youtube\-dl\ \-\-date\ 19700101

$\ #\ Download\ only\ the\ videos\ uploaded\ in\ the\ 200x\ decade
$\ youtube\-dl\ \-\-dateafter\ 20000101\ \-\-datebefore\ 20091231
\f[]
.fi
.SH FAQ
.SS How do I update youtube\-dl?
.PP
If you\[aq]ve followed our manual installation
instructions (https://ytdl-org.github.io/youtube-dl/download.html), you
can simply run \f[C]youtube\-dl\ \-U\f[] (or, on Linux,
\f[C]sudo\ youtube\-dl\ \-U\f[]).
.PP
If you have used pip, a simple
\f[C]sudo\ pip\ install\ \-U\ youtube\-dl\f[] is sufficient to update.
.PP
If you have installed youtube\-dl using a package manager like
\f[I]apt\-get\f[] or \f[I]yum\f[], use the standard system update
mechanism to update.
Note that distribution packages are often outdated.
As a rule of thumb, youtube\-dl releases at least once a month, and
often weekly or even daily.
Simply go to https://yt\-dl.org to find out the current version.
Unfortunately, there is nothing we youtube\-dl developers can do if your
distribution serves a really outdated version.
You can (and should) complain to your distribution in their bugtracker
or support forum.
.PP
As a last resort, you can also uninstall the version installed by your
package manager and follow our manual installation instructions.
For that, remove the distribution\[aq]s package, with a line like
.IP
.nf
\f[C]
sudo\ apt\-get\ remove\ \-y\ youtube\-dl
\f[]
.fi
.PP
Afterwards, simply follow our manual installation
instructions (https://ytdl-org.github.io/youtube-dl/download.html):
.IP
.nf
\f[C]
sudo\ wget\ https://yt\-dl.org/downloads/latest/youtube\-dl\ \-O\ /usr/local/bin/youtube\-dl
sudo\ chmod\ a+rx\ /usr/local/bin/youtube\-dl
hash\ \-r
\f[]
.fi
.PP
Again, from then on you\[aq]ll be able to update with
\f[C]sudo\ youtube\-dl\ \-U\f[].
.SS youtube\-dl is extremely slow to start on Windows
.PP
Add a file exclusion for \f[C]youtube\-dl.exe\f[] in Windows Defender
settings.
.SS I\[aq]m getting an error
\f[C]Unable\ to\ extract\ OpenGraph\ title\f[] on YouTube playlists
.PP
YouTube changed their playlist format in March 2014 and later on, so
you\[aq]ll need at least youtube\-dl 2014.07.25 to download all YouTube
videos.
.PP
If you have installed youtube\-dl with a package manager, pip, setup.py
or a tarball, please use that to update.
Note that Ubuntu packages do not seem to get updated anymore.
Since we are not affiliated with Ubuntu, there is little we can do.
Feel free to report
bugs (https://bugs.launchpad.net/ubuntu/+source/youtube-dl/+filebug) to
the Ubuntu packaging
people (mailto:<EMAIL>?subject=outdated%20version%20of%20youtube-dl)
\- all they have to do is update the package to a somewhat recent
version.
See above for a way to update.
.SS I\[aq]m getting an error when trying to use output template:
\f[C]error:\ using\ output\ template\ conflicts\ with\ using\ title,\ video\ ID\ or\ auto\ number\f[]
.PP
Make sure you are not using \f[C]\-o\f[] with any of these options
\f[C]\-t\f[], \f[C]\-\-title\f[], \f[C]\-\-id\f[], \f[C]\-A\f[] or
\f[C]\-\-auto\-number\f[] set in command line or in a configuration
file.
Remove the latter if any.
.SS Do I always have to pass \f[C]\-citw\f[]?
.PP
By default, youtube\-dl intends to have the best options (incidentally,
if you have a convincing case that these should be different, please
file an issue where you explain that (https://yt-dl.org/bug)).
Therefore, it is unnecessary and sometimes harmful to copy long option
strings from webpages.
In particular, the only option out of \f[C]\-citw\f[] that is regularly
useful is \f[C]\-i\f[].
.SS Can you please put the \f[C]\-b\f[] option back?
.PP
Most people asking this question are not aware that youtube\-dl now
defaults to downloading the highest available quality as reported by
YouTube, which will be 1080p or 720p in some cases, so you no longer
need the \f[C]\-b\f[] option.
For some specific videos, maybe YouTube does not report them to be
available in a specific high quality format you\[aq]re interested in.
In that case, simply request it with the \f[C]\-f\f[] option and
youtube\-dl will try to download it.
.SS I get HTTP error 402 when trying to download a video. What\[aq]s
this?
.PP
Apparently YouTube requires you to pass a CAPTCHA test if you download
too much.
We\[aq]re considering to provide a way to let you solve the
CAPTCHA (https://github.com/ytdl-org/youtube-dl/issues/154), but at the
moment, your best course of action is pointing a web browser to the
youtube URL, solving the CAPTCHA, and restart youtube\-dl.
.SS Do I need any other programs?
.PP
youtube\-dl works fine on its own on most sites.
However, if you want to convert video/audio, you\[aq]ll need
avconv (https://libav.org/) or ffmpeg (https://www.ffmpeg.org/).
On some sites \- most notably YouTube \- videos can be retrieved in a
higher quality format without sound.
youtube\-dl will detect whether avconv/ffmpeg is present and
automatically pick the best option.
.PP
Videos or video formats streamed via RTMP protocol can only be
downloaded when rtmpdump (https://rtmpdump.mplayerhq.hu/) is installed.
Downloading MMS and RTSP videos requires either
mplayer (https://mplayerhq.hu/) or mpv (https://mpv.io/) to be
installed.
.SS I have downloaded a video but how can I play it?
.PP
Once the video is fully downloaded, use any video player, such as
mpv (https://mpv.io/), vlc (https://www.videolan.org/) or
mplayer (https://www.mplayerhq.hu/).
.SS I extracted a video URL with \f[C]\-g\f[], but it does not play on
another machine / in my web browser.
.PP
It depends a lot on the service.
In many cases, requests for the video (to download/play it) must come
from the same IP address and with the same cookies and/or HTTP headers.
Use the \f[C]\-\-cookies\f[] option to write the required cookies into a
file, and advise your downloader to read cookies from that file.
Some sites also require a common user agent to be used, use
\f[C]\-\-dump\-user\-agent\f[] to see the one in use by youtube\-dl.
You can also get necessary cookies and HTTP headers from JSON output
obtained with \f[C]\-\-dump\-json\f[].
.PP
It may be beneficial to use IPv6; in some cases, the restrictions are
only applied to IPv4.
Some services (sometimes only for a subset of videos) do not restrict
the video URL by IP address, cookie, or user\-agent, but these are the
exception rather than the rule.
.PP
Please bear in mind that some URL protocols are \f[B]not\f[] supported
by browsers out of the box, including RTMP.
If you are using \f[C]\-g\f[], your own downloader must support these as
well.
.PP
If you want to play the video on a machine that is not running
youtube\-dl, you can relay the video content from the machine that runs
youtube\-dl.
You can use \f[C]\-o\ \-\f[] to let youtube\-dl stream a video to
stdout, or simply allow the player to download the files written by
youtube\-dl in turn.
.SS ERROR: no fmt_url_map or conn information found in video info
.PP
YouTube has switched to a new video info format in July 2011 which is
not supported by old versions of youtube\-dl.
See above for how to update youtube\-dl.
.SS ERROR: unable to download video
.PP
YouTube requires an additional signature since September 2012 which is
not supported by old versions of youtube\-dl.
See above for how to update youtube\-dl.
.SS Video URL contains an ampersand and I\[aq]m getting some strange
output \f[C][1]\ 2839\f[] or
\f[C]\[aq]v\[aq]\ is\ not\ recognized\ as\ an\ internal\ or\ external\ command\f[]
.PP
That\[aq]s actually the output from your shell.
Since ampersand is one of the special shell characters it\[aq]s
interpreted by the shell preventing you from passing the whole URL to
youtube\-dl.
To disable your shell from interpreting the ampersands (or any other
special characters) you have to either put the whole URL in quotes or
escape them with a backslash (which approach will work depends on your
shell).
.PP
For example if your URL is
https://www.youtube.com/watch?t=4&v=BaW_jenozKc you should end up with
following command:
.PP
\f[C]youtube\-dl\ \[aq]https://www.youtube.com/watch?t=4&v=BaW_jenozKc\[aq]\f[]
.PP
or
.PP
\f[C]youtube\-dl\ https://www.youtube.com/watch?t=4\\&v=BaW_jenozKc\f[]
.PP
For Windows you have to use the double quotes:
.PP
\f[C]youtube\-dl\ "https://www.youtube.com/watch?t=4&v=BaW_jenozKc"\f[]
.SS ExtractorError: Could not find JS function u\[aq]OF\[aq]
.PP
In February 2015, the new YouTube player contained a character sequence
in a string that was misinterpreted by old versions of youtube\-dl.
See above for how to update youtube\-dl.
.SS HTTP Error 429: Too Many Requests or 402: Payment Required
.PP
These two error codes indicate that the service is blocking your IP
address because of overuse.
Usually this is a soft block meaning that you can gain access again
after solving CAPTCHA.
Just open a browser and solve a CAPTCHA the service suggests you and
after that pass cookies to youtube\-dl.
Note that if your machine has multiple external IPs then you should also
pass exactly the same IP you\[aq]ve used for solving CAPTCHA with
\f[C]\-\-source\-address\f[].
Also you may need to pass a \f[C]User\-Agent\f[] HTTP header of your
browser with \f[C]\-\-user\-agent\f[].
.PP
If this is not the case (no CAPTCHA suggested to solve by the service)
then you can contact the service and ask them to unblock your IP
address, or \- if you have acquired a whitelisted IP address already \-
use the \f[C]\-\-proxy\f[] or \f[C]\-\-source\-address\f[] options to
select another IP address.
.SS SyntaxError: Non\-ASCII character
.PP
The error
.IP
.nf
\f[C]
File\ "youtube\-dl",\ line\ 2
SyntaxError:\ Non\-ASCII\ character\ \[aq]\\x93\[aq]\ ...
\f[]
.fi
.PP
means you\[aq]re using an outdated version of Python.
Please update to Python 2.6 or 2.7.
.SS What is this binary file? Where has the code gone?
.PP
Since June 2012
(#342 (https://github.com/ytdl-org/youtube-dl/issues/342)) youtube\-dl
is packed as an executable zipfile, simply unzip it (might need renaming
to \f[C]youtube\-dl.zip\f[] first on some systems) or clone the git
repository, as laid out above.
If you modify the code, you can run it by executing the
\f[C]__main__.py\f[] file.
To recompile the executable, run \f[C]make\ youtube\-dl\f[].
.SS The exe throws an error due to missing \f[C]MSVCR100.dll\f[]
.PP
To run the exe you need to install first the Microsoft Visual C++ 2010
Service Pack 1 Redistributable Package
(x86) (https://download.microsoft.com/download/1/6/5/165255E7-1014-4D0A-B094-B6A430A6BFFC/vcredist_x86.exe).
.SS On Windows, how should I set up ffmpeg and youtube\-dl? Where should
I put the exe files?
.PP
If you put youtube\-dl and ffmpeg in the same directory that you\[aq]re
running the command from, it will work, but that\[aq]s rather
cumbersome.
.PP
To make a different directory work \- either for ffmpeg, or for
youtube\-dl, or for both \- simply create the directory (say,
\f[C]C:\\bin\f[], or \f[C]C:\\Users\\<USER>\ name>\\bin\f[]), put all the
executables directly in there, and then set your PATH environment
variable (https://www.java.com/en/download/help/path.xml) to include
that directory.
.PP
From then on, after restarting your shell, you will be able to access
both youtube\-dl and ffmpeg (and youtube\-dl will be able to find
ffmpeg) by simply typing \f[C]youtube\-dl\f[] or \f[C]ffmpeg\f[], no
matter what directory you\[aq]re in.
.SS How do I put downloads into a specific folder?
.PP
Use the \f[C]\-o\f[] to specify an output template, for example
\f[C]\-o\ "/home/<USER>/videos/%(title)s\-%(id)s.%(ext)s"\f[].
If you want this for all of your downloads, put the option into your
configuration file.
.SS How do I download a video starting with a \f[C]\-\f[]?
.PP
Either prepend \f[C]https://www.youtube.com/watch?v=\f[] or separate the
ID from the options with \f[C]\-\-\f[]:
.IP
.nf
\f[C]
youtube\-dl\ \-\-\ \-wNyEUrxzFU
youtube\-dl\ "https://www.youtube.com/watch?v=\-wNyEUrxzFU"
\f[]
.fi
.SS How do I pass cookies to youtube\-dl?
.PP
Use the \f[C]\-\-cookies\f[] option, for example
\f[C]\-\-cookies\ /path/to/cookies/file.txt\f[].
.PP
In order to extract cookies from browser use any conforming browser
extension for exporting cookies.
For example, Get
cookies.txt (https://chrome.google.com/webstore/detail/get-cookiestxt/bgaddhkoddajcdgocldbbfleckgcbcid/)
(for Chrome) or
cookies.txt (https://addons.mozilla.org/en-US/firefox/addon/cookies-txt/)
(for Firefox).
.PP
Note that the cookies file must be in Mozilla/Netscape format and the
first line of the cookies file must be either
\f[C]#\ HTTP\ Cookie\ File\f[] or
\f[C]#\ Netscape\ HTTP\ Cookie\ File\f[].
Make sure you have correct newline
format (https://en.wikipedia.org/wiki/Newline) in the cookies file and
convert newlines if necessary to correspond with your OS, namely
\f[C]CRLF\f[] (\f[C]\\r\\n\f[]) for Windows and \f[C]LF\f[]
(\f[C]\\n\f[]) for Unix and Unix\-like systems (Linux, macOS, etc.).
\f[C]HTTP\ Error\ 400:\ Bad\ Request\f[] when using \f[C]\-\-cookies\f[]
is a good sign of invalid newline format.
.PP
Passing cookies to youtube\-dl is a good way to workaround login when a
particular extractor does not implement it explicitly.
Another use case is working around
CAPTCHA (https://en.wikipedia.org/wiki/CAPTCHA) some websites require
you to solve in particular cases in order to get access (e.g.
YouTube, CloudFlare).
.SS How do I stream directly to media player?
.PP
You will first need to tell youtube\-dl to stream media to stdout with
\f[C]\-o\ \-\f[], and also tell your media player to read from stdin (it
must be capable of this for streaming) and then pipe former to latter.
For example, streaming to vlc (https://www.videolan.org/) can be
achieved with:
.IP
.nf
\f[C]
youtube\-dl\ \-o\ \-\ "https://www.youtube.com/watch?v=BaW_jenozKcj"\ |\ vlc\ \-
\f[]
.fi
.SS How do I download only new videos from a playlist?
.PP
Use download\-archive feature.
With this feature you should initially download the complete playlist
with \f[C]\-\-download\-archive\ /path/to/download/archive/file.txt\f[]
that will record identifiers of all the videos in a special file.
Each subsequent run with the same \f[C]\-\-download\-archive\f[] will
download only new videos and skip all videos that have been downloaded
before.
Note that only successful downloads are recorded in the file.
.PP
For example, at first,
.IP
.nf
\f[C]
youtube\-dl\ \-\-download\-archive\ archive.txt\ "https://www.youtube.com/playlist?list=PLwiyx1dc3P2JR9N8gQaQN_BCvlSlap7re"
\f[]
.fi
.PP
will download the complete \f[C]PLwiyx1dc3P2JR9N8gQaQN_BCvlSlap7re\f[]
playlist and create a file \f[C]archive.txt\f[].
Each subsequent run will only download new videos if any:
.IP
.nf
\f[C]
youtube\-dl\ \-\-download\-archive\ archive.txt\ "https://www.youtube.com/playlist?list=PLwiyx1dc3P2JR9N8gQaQN_BCvlSlap7re"
\f[]
.fi
.SS Should I add \f[C]\-\-hls\-prefer\-native\f[] into my config?
.PP
When youtube\-dl detects an HLS video, it can download it either with
the built\-in downloader or ffmpeg.
Since many HLS streams are slightly invalid and ffmpeg/youtube\-dl each
handle some invalid cases better than the other, there is an option to
switch the downloader if needed.
.PP
When youtube\-dl knows that one particular downloader works better for a
given website, that downloader will be picked.
Otherwise, youtube\-dl will pick the best downloader for general
compatibility, which at the moment happens to be ffmpeg.
This choice may change in future versions of youtube\-dl, with
improvements of the built\-in downloader and/or ffmpeg.
.PP
In particular, the generic extractor (used when your website is not in
the list of supported sites by
youtube\-dl (https://ytdl-org.github.io/youtube-dl/supportedsites.html)
cannot mandate one specific downloader.
.PP
If you put either \f[C]\-\-hls\-prefer\-native\f[] or
\f[C]\-\-hls\-prefer\-ffmpeg\f[] into your configuration, a different
subset of videos will fail to download correctly.
Instead, it is much better to file an issue (https://yt-dl.org/bug) or a
pull request which details why the native or the ffmpeg HLS downloader
is a better choice for your use case.
.SS Can you add support for this anime video site, or site which shows
current movies for free?
.PP
As a matter of policy (as well as legality), youtube\-dl does not
include support for services that specialize in infringing copyright.
As a rule of thumb, if you cannot easily find a video that the service
is quite obviously allowed to distribute (i.e.
that has been uploaded by the creator, the creator\[aq]s distributor, or
is published under a free license), the service is probably unfit for
inclusion to youtube\-dl.
.PP
A note on the service that they don\[aq]t host the infringing content,
but just link to those who do, is evidence that the service should
\f[B]not\f[] be included into youtube\-dl.
The same goes for any DMCA note when the whole front page of the service
is filled with videos they are not allowed to distribute.
A "fair use" note is equally unconvincing if the service shows
copyright\-protected videos in full without authorization.
.PP
Support requests for services that \f[B]do\f[] purchase the rights to
distribute their content are perfectly fine though.
If in doubt, you can simply include a source that mentions the
legitimate purchase of content.
.SS How can I speed up work on my issue?
.PP
(Also known as: Help, my important issue not being solved!) The
youtube\-dl core developer team is quite small.
While we do our best to solve as many issues as possible, sometimes that
can take quite a while.
To speed up your issue, here\[aq]s what you can do:
.PP
First of all, please do report the issue at our issue
tracker (https://yt-dl.org/bugs).
That allows us to coordinate all efforts by users and developers, and
serves as a unified point.
Unfortunately, the youtube\-dl project has grown too large to use
personal email as an effective communication channel.
.PP
Please read the bug reporting instructions below.
A lot of bugs lack all the necessary information.
If you can, offer proxy, VPN, or shell access to the youtube\-dl
developers.
If you are able to, test the issue from multiple computers in multiple
countries to exclude local censorship or misconfiguration issues.
.PP
If nobody is interested in solving your issue, you are welcome to take
matters into your own hands and submit a pull request (or coerce/pay
somebody else to do so).
.PP
Feel free to bump the issue from time to time by writing a small comment
("Issue is still present in youtube\-dl version ...from France, but
fixed from Belgium"), but please not more than once a month.
Please do not declare your issue as \f[C]important\f[] or
\f[C]urgent\f[].
.SS How can I detect whether a given URL is supported by youtube\-dl?
.PP
For one, have a look at the list of supported
sites (docs/supportedsites.md).
Note that it can sometimes happen that the site changes its URL scheme
(say, from https://example.com/video/1234567 to
https://example.com/v/1234567 ) and youtube\-dl reports an URL of a
service in that list as unsupported.
In that case, simply report a bug.
.PP
It is \f[I]not\f[] possible to detect whether a URL is supported or not.
That\[aq]s because youtube\-dl contains a generic extractor which
matches \f[B]all\f[] URLs.
You may be tempted to disable, exclude, or remove the generic extractor,
but the generic extractor not only allows users to extract videos from
lots of websites that embed a video from another service, but may also
be used to extract video from a service that it\[aq]s hosting itself.
Therefore, we neither recommend nor support disabling, excluding, or
removing the generic extractor.
.PP
If you want to find out whether a given URL is supported, simply call
youtube\-dl with it.
If you get no videos back, chances are the URL is either not referring
to a video or unsupported.
You can find out which by examining the output (if you run youtube\-dl
on the console) or catching an \f[C]UnsupportedError\f[] exception if
you run it from a Python program.
.SH Why do I need to go through that much red tape when filing bugs?
.PP
Before we had the issue template, despite our extensive bug reporting
instructions, about 80% of the issue reports we got were useless, for
instance because people used ancient versions hundreds of releases old,
because of simple syntactic errors (not in youtube\-dl but in general
shell usage), because the problem was already reported multiple times
before, because people did not actually read an error message, even if
it said "please install ffmpeg", because people did not mention the URL
they were trying to download and many more simple, easy\-to\-avoid
problems, many of whom were totally unrelated to youtube\-dl.
.PP
youtube\-dl is an open\-source project manned by too few volunteers, so
we\[aq]d rather spend time fixing bugs where we are certain none of
those simple problems apply, and where we can be reasonably confident to
be able to reproduce the issue without asking the reporter repeatedly.
As such, the output of \f[C]youtube\-dl\ \-v\ YOUR_URL_HERE\f[] is
really all that\[aq]s required to file an issue.
The issue template also guides you through some basic steps you can do,
such as checking that your version of youtube\-dl is current.
.SH DEVELOPER INSTRUCTIONS
.PP
Most users do not need to build youtube\-dl and can download the
builds (https://ytdl-org.github.io/youtube-dl/download.html) or get them
from their distribution.
.PP
To run youtube\-dl as a developer, you don\[aq]t need to build anything
either.
Simply execute
.IP
.nf
\f[C]
python\ \-m\ youtube_dl
\f[]
.fi
.PP
To run the test, simply invoke your favorite test runner, or execute a
test file directly; any of the following work:
.IP
.nf
\f[C]
python\ \-m\ unittest\ discover
python\ test/test_download.py
nosetests
\f[]
.fi
.PP
See item 6 of new extractor tutorial for how to run extractor specific
test cases.
.PP
If you want to create a build of youtube\-dl yourself, you\[aq]ll need
.IP \[bu] 2
python
.IP \[bu] 2
make (only GNU make is supported)
.IP \[bu] 2
pandoc
.IP \[bu] 2
zip
.IP \[bu] 2
nosetests
.SS Adding support for a new site
.PP
If you want to add support for a new site, first of all \f[B]make
sure\f[] this site is \f[B]not dedicated to copyright
infringement (README.md#can-you-add-support-for-this-anime-video-site-or-site-which-shows-current-movies-for-free)\f[].
youtube\-dl does \f[B]not support\f[] such sites thus pull requests
adding support for them \f[B]will be rejected\f[].
.PP
After you have ensured this site is distributing its content legally,
you can follow this quick list (assuming your service is called
\f[C]yourextractor\f[]):
.IP " 1." 4
Fork this repository (https://github.com/ytdl-org/youtube-dl/fork)
.IP " 2." 4
Check out the source code with:
.RS 4
.IP
.nf
\f[C]
\ git\ clone\ git\@github.com:YOUR_GITHUB_USERNAME/youtube\-dl.git
\f[]
.fi
.RE
.IP " 3." 4
Start a new git branch with
.RS 4
.IP
.nf
\f[C]
\ cd\ youtube\-dl
\ git\ checkout\ \-b\ yourextractor
\f[]
.fi
.RE
.IP " 4." 4
Start with this simple template and save it to
\f[C]youtube_dl/extractor/yourextractor.py\f[]:
.RS 4
.IP
.nf
\f[C]
#\ coding:\ utf\-8
from\ __future__\ import\ unicode_literals

from\ .common\ import\ InfoExtractor


class\ YourExtractorIE(InfoExtractor):
\ \ \ \ _VALID_URL\ =\ r\[aq]https?://(?:www\\.)?yourextractor\\.com/watch/(?P<id>[0\-9]+)\[aq]
\ \ \ \ _TEST\ =\ {
\ \ \ \ \ \ \ \ \[aq]url\[aq]:\ \[aq]https://yourextractor.com/watch/42\[aq],
\ \ \ \ \ \ \ \ \[aq]md5\[aq]:\ \[aq]TODO:\ md5\ sum\ of\ the\ first\ 10241\ bytes\ of\ the\ video\ file\ (use\ \-\-test)\[aq],
\ \ \ \ \ \ \ \ \[aq]info_dict\[aq]:\ {
\ \ \ \ \ \ \ \ \ \ \ \ \[aq]id\[aq]:\ \[aq]42\[aq],
\ \ \ \ \ \ \ \ \ \ \ \ \[aq]ext\[aq]:\ \[aq]mp4\[aq],
\ \ \ \ \ \ \ \ \ \ \ \ \[aq]title\[aq]:\ \[aq]Video\ title\ goes\ here\[aq],
\ \ \ \ \ \ \ \ \ \ \ \ \[aq]thumbnail\[aq]:\ r\[aq]re:^https?://.*\\.jpg$\[aq],
\ \ \ \ \ \ \ \ \ \ \ \ #\ TODO\ more\ properties,\ either\ as:
\ \ \ \ \ \ \ \ \ \ \ \ #\ *\ A\ value
\ \ \ \ \ \ \ \ \ \ \ \ #\ *\ MD5\ checksum;\ start\ the\ string\ with\ md5:
\ \ \ \ \ \ \ \ \ \ \ \ #\ *\ A\ regular\ expression;\ start\ the\ string\ with\ re:
\ \ \ \ \ \ \ \ \ \ \ \ #\ *\ Any\ Python\ type\ (for\ example\ int\ or\ float)
\ \ \ \ \ \ \ \ }
\ \ \ \ }

\ \ \ \ def\ _real_extract(self,\ url):
\ \ \ \ \ \ \ \ video_id\ =\ self._match_id(url)
\ \ \ \ \ \ \ \ webpage\ =\ self._download_webpage(url,\ video_id)

\ \ \ \ \ \ \ \ #\ TODO\ more\ code\ goes\ here,\ for\ example\ ...
\ \ \ \ \ \ \ \ title\ =\ self._html_search_regex(r\[aq]<h1>(.+?)</h1>\[aq],\ webpage,\ \[aq]title\[aq])

\ \ \ \ \ \ \ \ return\ {
\ \ \ \ \ \ \ \ \ \ \ \ \[aq]id\[aq]:\ video_id,
\ \ \ \ \ \ \ \ \ \ \ \ \[aq]title\[aq]:\ title,
\ \ \ \ \ \ \ \ \ \ \ \ \[aq]description\[aq]:\ self._og_search_description(webpage),
\ \ \ \ \ \ \ \ \ \ \ \ \[aq]uploader\[aq]:\ self._search_regex(r\[aq]<div[^>]+id="uploader"[^>]*>([^<]+)<\[aq],\ webpage,\ \[aq]uploader\[aq],\ fatal=False),
\ \ \ \ \ \ \ \ \ \ \ \ #\ TODO\ more\ properties\ (see\ youtube_dl/extractor/common.py)
\ \ \ \ \ \ \ \ }
\f[]
.fi
.RE
.IP " 5." 4
Add an import in
\f[C]youtube_dl/extractor/extractors.py\f[] (https://github.com/ytdl-org/youtube-dl/blob/master/youtube_dl/extractor/extractors.py).
.IP " 6." 4
Run
\f[C]python\ test/test_download.py\ TestDownload.test_YourExtractor\f[].
This \f[I]should fail\f[] at first, but you can continually re\-run it
until you\[aq]re done.
If you decide to add more than one test, then rename \f[C]_TEST\f[] to
\f[C]_TESTS\f[] and make it into a list of dictionaries.
The tests will then be named \f[C]TestDownload.test_YourExtractor\f[],
\f[C]TestDownload.test_YourExtractor_1\f[],
\f[C]TestDownload.test_YourExtractor_2\f[], etc.
Note that tests with \f[C]only_matching\f[] key in test\[aq]s dict are
not counted in.
.IP " 7." 4
Have a look at
\f[C]youtube_dl/extractor/common.py\f[] (https://github.com/ytdl-org/youtube-dl/blob/master/youtube_dl/extractor/common.py)
for possible helper methods and a detailed description of what your
extractor should and may
return (https://github.com/ytdl-org/youtube-dl/blob/7f41a598b3fba1bcab2817de64a08941200aa3c8/youtube_dl/extractor/common.py#L94-L303).
Add tests and code for as many as you want.
.IP " 8." 4
Make sure your code follows youtube\-dl coding conventions and check the
code with
flake8 (https://flake8.pycqa.org/en/latest/index.html#quickstart):
.RS 4
.IP
.nf
\f[C]
\ $\ flake8\ youtube_dl/extractor/yourextractor.py
\f[]
.fi
.RE
.IP " 9." 4
Make sure your code works under all Python (https://www.python.org/)
versions claimed supported by youtube\-dl, namely 2.6, 2.7, and 3.2+.
.IP "10." 4
When the tests pass, add (https://git-scm.com/docs/git-add) the new
files and commit (https://git-scm.com/docs/git-commit) them and
push (https://git-scm.com/docs/git-push) the result, like this:
.RS 4
.IP
.nf
\f[C]
$\ git\ add\ youtube_dl/extractor/extractors.py
$\ git\ add\ youtube_dl/extractor/yourextractor.py
$\ git\ commit\ \-m\ \[aq][yourextractor]\ Add\ new\ extractor\[aq]
$\ git\ push\ origin\ yourextractor
\f[]
.fi
.RE
.IP "11." 4
Finally, create a pull
request (https://help.github.com/articles/creating-a-pull-request).
We\[aq]ll then review and merge it.
.PP
In any case, thank you very much for your contributions!
.SS youtube\-dl coding conventions
.PP
This section introduces a guide lines for writing idiomatic, robust and
future\-proof extractor code.
.PP
Extractors are very fragile by nature since they depend on the layout of
the source data provided by 3rd party media hosters out of your control
and this layout tends to change.
As an extractor implementer your task is not only to write code that
will extract media links and metadata correctly but also to minimize
dependency on the source\[aq]s layout and even to make the code foresee
potential future changes and be ready for that.
This is important because it will allow the extractor not to break on
minor layout changes thus keeping old youtube\-dl versions working.
Even though this breakage issue is easily fixed by emitting a new
version of youtube\-dl with a fix incorporated, all the previous
versions become broken in all repositories and distros\[aq] packages
that may not be so prompt in fetching the update from us.
Needless to say, some non rolling release distros may never receive an
update at all.
.SS Mandatory and optional metafields
.PP
For extraction to work youtube\-dl relies on metadata your extractor
extracts and provides to youtube\-dl expressed by an information
dictionary (https://github.com/ytdl-org/youtube-dl/blob/7f41a598b3fba1bcab2817de64a08941200aa3c8/youtube_dl/extractor/common.py#L94-L303)
or simply \f[I]info dict\f[].
Only the following meta fields in the \f[I]info dict\f[] are considered
mandatory for a successful extraction process by youtube\-dl:
.IP \[bu] 2
\f[C]id\f[] (media identifier)
.IP \[bu] 2
\f[C]title\f[] (media title)
.IP \[bu] 2
\f[C]url\f[] (media download URL) or \f[C]formats\f[]
.PP
In fact only the last option is technically mandatory (i.e.
if you can\[aq]t figure out the download location of the media the
extraction does not make any sense).
But by convention youtube\-dl also treats \f[C]id\f[] and \f[C]title\f[]
as mandatory.
Thus the aforementioned metafields are the critical data that the
extraction does not make any sense without and if any of them fail to be
extracted then the extractor is considered completely broken.
.PP
Any
field (https://github.com/ytdl-org/youtube-dl/blob/7f41a598b3fba1bcab2817de64a08941200aa3c8/youtube_dl/extractor/common.py#L188-L303)
apart from the aforementioned ones are considered \f[B]optional\f[].
That means that extraction should be \f[B]tolerant\f[] to situations
when sources for these fields can potentially be unavailable (even if
they are always available at the moment) and \f[B]future\-proof\f[] in
order not to break the extraction of general purpose mandatory fields.
.SS Example
.PP
Say you have some source dictionary \f[C]meta\f[] that you\[aq]ve
fetched as JSON with HTTP request and it has a key \f[C]summary\f[]:
.IP
.nf
\f[C]
meta\ =\ self._download_json(url,\ video_id)
\f[]
.fi
.PP
Assume at this point \f[C]meta\f[]\[aq]s layout is:
.IP
.nf
\f[C]
{
\ \ \ \ ...
\ \ \ \ "summary":\ "some\ fancy\ summary\ text",
\ \ \ \ ...
}
\f[]
.fi
.PP
Assume you want to extract \f[C]summary\f[] and put it into the
resulting info dict as \f[C]description\f[].
Since \f[C]description\f[] is an optional meta field you should be ready
that this key may be missing from the \f[C]meta\f[] dict, so that you
should extract it like:
.IP
.nf
\f[C]
description\ =\ meta.get(\[aq]summary\[aq])\ \ #\ correct
\f[]
.fi
.PP
and not like:
.IP
.nf
\f[C]
description\ =\ meta[\[aq]summary\[aq]]\ \ #\ incorrect
\f[]
.fi
.PP
The latter will break extraction process with \f[C]KeyError\f[] if
\f[C]summary\f[] disappears from \f[C]meta\f[] at some later time but
with the former approach extraction will just go ahead with
\f[C]description\f[] set to \f[C]None\f[] which is perfectly fine
(remember \f[C]None\f[] is equivalent to the absence of data).
.PP
Similarly, you should pass \f[C]fatal=False\f[] when extracting optional
data from a webpage with \f[C]_search_regex\f[],
\f[C]_html_search_regex\f[] or similar methods, for instance:
.IP
.nf
\f[C]
description\ =\ self._search_regex(
\ \ \ \ r\[aq]<span[^>]+id="title"[^>]*>([^<]+)<\[aq],
\ \ \ \ webpage,\ \[aq]description\[aq],\ fatal=False)
\f[]
.fi
.PP
With \f[C]fatal\f[] set to \f[C]False\f[] if \f[C]_search_regex\f[]
fails to extract \f[C]description\f[] it will emit a warning and
continue extraction.
.PP
You can also pass \f[C]default=<some\ fallback\ value>\f[], for example:
.IP
.nf
\f[C]
description\ =\ self._search_regex(
\ \ \ \ r\[aq]<span[^>]+id="title"[^>]*>([^<]+)<\[aq],
\ \ \ \ webpage,\ \[aq]description\[aq],\ default=None)
\f[]
.fi
.PP
On failure this code will silently continue the extraction with
\f[C]description\f[] set to \f[C]None\f[].
That is useful for metafields that may or may not be present.
.SS Provide fallbacks
.PP
When extracting metadata try to do so from multiple sources.
For example if \f[C]title\f[] is present in several places, try
extracting from at least some of them.
This makes it more future\-proof in case some of the sources become
unavailable.
.SS Example
.PP
Say \f[C]meta\f[] from the previous example has a \f[C]title\f[] and you
are about to extract it.
Since \f[C]title\f[] is a mandatory meta field you should end up with
something like:
.IP
.nf
\f[C]
title\ =\ meta[\[aq]title\[aq]]
\f[]
.fi
.PP
If \f[C]title\f[] disappears from \f[C]meta\f[] in future due to some
changes on the hoster\[aq]s side the extraction would fail since
\f[C]title\f[] is mandatory.
That\[aq]s expected.
.PP
Assume that you have some another source you can extract \f[C]title\f[]
from, for example \f[C]og:title\f[] HTML meta of a \f[C]webpage\f[].
In this case you can provide a fallback scenario:
.IP
.nf
\f[C]
title\ =\ meta.get(\[aq]title\[aq])\ or\ self._og_search_title(webpage)
\f[]
.fi
.PP
This code will try to extract from \f[C]meta\f[] first and if it fails
it will try extracting \f[C]og:title\f[] from a \f[C]webpage\f[].
.SS Regular expressions
.SS Don\[aq]t capture groups you don\[aq]t use
.PP
Capturing group must be an indication that it\[aq]s used somewhere in
the code.
Any group that is not used must be non capturing.
.SS Example
.PP
Don\[aq]t capture id attribute name here since you can\[aq]t use it for
anything anyway.
.PP
Correct:
.IP
.nf
\f[C]
r\[aq](?:id|ID)=(?P<id>\\d+)\[aq]
\f[]
.fi
.PP
Incorrect:
.IP
.nf
\f[C]
r\[aq](id|ID)=(?P<id>\\d+)\[aq]
\f[]
.fi
.SS Make regular expressions relaxed and flexible
.PP
When using regular expressions try to write them fuzzy, relaxed and
flexible, skipping insignificant parts that are more likely to change,
allowing both single and double quotes for quoted values and so on.
.SS Example
.PP
Say you need to extract \f[C]title\f[] from the following HTML code:
.IP
.nf
\f[C]
<span\ style="position:\ absolute;\ left:\ 910px;\ width:\ 90px;\ float:\ right;\ z\-index:\ 9999;"\ class="title">some\ fancy\ title</span>
\f[]
.fi
.PP
The code for that task should look similar to:
.IP
.nf
\f[C]
title\ =\ self._search_regex(
\ \ \ \ r\[aq]<span[^>]+class="title"[^>]*>([^<]+)\[aq],\ webpage,\ \[aq]title\[aq])
\f[]
.fi
.PP
Or even better:
.IP
.nf
\f[C]
title\ =\ self._search_regex(
\ \ \ \ r\[aq]<span[^>]+class=(["\\\[aq]])title\\1[^>]*>(?P<title>[^<]+)\[aq],
\ \ \ \ webpage,\ \[aq]title\[aq],\ group=\[aq]title\[aq])
\f[]
.fi
.PP
Note how you tolerate potential changes in the \f[C]style\f[]
attribute\[aq]s value or switch from using double quotes to single for
\f[C]class\f[] attribute:
.PP
The code definitely should not look like:
.IP
.nf
\f[C]
title\ =\ self._search_regex(
\ \ \ \ r\[aq]<span\ style="position:\ absolute;\ left:\ 910px;\ width:\ 90px;\ float:\ right;\ z\-index:\ 9999;"\ class="title">(.*?)</span>\[aq],
\ \ \ \ webpage,\ \[aq]title\[aq],\ group=\[aq]title\[aq])
\f[]
.fi
.SS Long lines policy
.PP
There is a soft limit to keep lines of code under 80 characters long.
This means it should be respected if possible and if it does not make
readability and code maintenance worse.
.PP
For example, you should \f[B]never\f[] split long string literals like
URLs or some other often copied entities over multiple lines to fit this
limit:
.PP
Correct:
.IP
.nf
\f[C]
\[aq]https://www.youtube.com/watch?v=FqZTN594JQw&list=PLMYEtVRpaqY00V9W81Cwmzp6N6vZqfUKD4\[aq]
\f[]
.fi
.PP
Incorrect:
.IP
.nf
\f[C]
\[aq]https://www.youtube.com/watch?v=FqZTN594JQw&list=\[aq]
\[aq]PLMYEtVRpaqY00V9W81Cwmzp6N6vZqfUKD4\[aq]
\f[]
.fi
.SS Inline values
.PP
Extracting variables is acceptable for reducing code duplication and
improving readability of complex expressions.
However, you should avoid extracting variables used only once and moving
them to opposite parts of the extractor file, which makes reading the
linear flow difficult.
.SS Example
.PP
Correct:
.IP
.nf
\f[C]
title\ =\ self._html_search_regex(r\[aq]<title>([^<]+)</title>\[aq],\ webpage,\ \[aq]title\[aq])
\f[]
.fi
.PP
Incorrect:
.IP
.nf
\f[C]
TITLE_RE\ =\ r\[aq]<title>([^<]+)</title>\[aq]
#\ ...some\ lines\ of\ code...
title\ =\ self._html_search_regex(TITLE_RE,\ webpage,\ \[aq]title\[aq])
\f[]
.fi
.SS Collapse fallbacks
.PP
Multiple fallback values can quickly become unwieldy.
Collapse multiple fallback values into a single expression via a list of
patterns.
.SS Example
.PP
Good:
.IP
.nf
\f[C]
description\ =\ self._html_search_meta(
\ \ \ \ [\[aq]og:description\[aq],\ \[aq]description\[aq],\ \[aq]twitter:description\[aq]],
\ \ \ \ webpage,\ \[aq]description\[aq],\ default=None)
\f[]
.fi
.PP
Unwieldy:
.IP
.nf
\f[C]
description\ =\ (
\ \ \ \ self._og_search_description(webpage,\ default=None)
\ \ \ \ or\ self._html_search_meta(\[aq]description\[aq],\ webpage,\ default=None)
\ \ \ \ or\ self._html_search_meta(\[aq]twitter:description\[aq],\ webpage,\ default=None))
\f[]
.fi
.PP
Methods supporting list of patterns are: \f[C]_search_regex\f[],
\f[C]_html_search_regex\f[], \f[C]_og_search_property\f[],
\f[C]_html_search_meta\f[].
.SS Trailing parentheses
.PP
Always move trailing parentheses after the last argument.
.SS Example
.PP
Correct:
.IP
.nf
\f[C]
\ \ \ \ lambda\ x:\ x[\[aq]ResultSet\[aq]][\[aq]Result\[aq]][0][\[aq]VideoUrlSet\[aq]][\[aq]VideoUrl\[aq]],
\ \ \ \ list)
\f[]
.fi
.PP
Incorrect:
.IP
.nf
\f[C]
\ \ \ \ lambda\ x:\ x[\[aq]ResultSet\[aq]][\[aq]Result\[aq]][0][\[aq]VideoUrlSet\[aq]][\[aq]VideoUrl\[aq]],
\ \ \ \ list,
)
\f[]
.fi
.SS Use convenience conversion and parsing functions
.PP
Wrap all extracted numeric data into safe functions from
\f[C]youtube_dl/utils.py\f[] (https://github.com/ytdl-org/youtube-dl/blob/master/youtube_dl/utils.py):
\f[C]int_or_none\f[], \f[C]float_or_none\f[].
Use them for string to number conversions as well.
.PP
Use \f[C]url_or_none\f[] for safe URL processing.
.PP
Use \f[C]try_get\f[] for safe metadata extraction from parsed JSON.
.PP
Use \f[C]unified_strdate\f[] for uniform \f[C]upload_date\f[] or any
\f[C]YYYYMMDD\f[] meta field extraction, \f[C]unified_timestamp\f[] for
uniform \f[C]timestamp\f[] extraction, \f[C]parse_filesize\f[] for
\f[C]filesize\f[] extraction, \f[C]parse_count\f[] for count meta fields
extraction, \f[C]parse_resolution\f[], \f[C]parse_duration\f[] for
\f[C]duration\f[] extraction, \f[C]parse_age_limit\f[] for
\f[C]age_limit\f[] extraction.
.PP
Explore
\f[C]youtube_dl/utils.py\f[] (https://github.com/ytdl-org/youtube-dl/blob/master/youtube_dl/utils.py)
for more useful convenience functions.
.SS More examples
.SS Safely extract optional description from parsed JSON
.IP
.nf
\f[C]
description\ =\ try_get(response,\ lambda\ x:\ x[\[aq]result\[aq]][\[aq]video\[aq]][0][\[aq]summary\[aq]],\ compat_str)
\f[]
.fi
.SS Safely extract more optional metadata
.IP
.nf
\f[C]
video\ =\ try_get(response,\ lambda\ x:\ x[\[aq]result\[aq]][\[aq]video\[aq]][0],\ dict)\ or\ {}
description\ =\ video.get(\[aq]summary\[aq])
duration\ =\ float_or_none(video.get(\[aq]durationMs\[aq]),\ scale=1000)
view_count\ =\ int_or_none(video.get(\[aq]views\[aq]))
\f[]
.fi
.SH EMBEDDING YOUTUBE\-DL
.PP
youtube\-dl makes the best effort to be a good command\-line program,
and thus should be callable from any programming language.
If you encounter any problems parsing its output, feel free to create a
report (https://github.com/ytdl-org/youtube-dl/issues/new).
.PP
From a Python program, you can embed youtube\-dl in a more powerful
fashion, like this:
.IP
.nf
\f[C]
from\ __future__\ import\ unicode_literals
import\ youtube_dl

ydl_opts\ =\ {}
with\ youtube_dl.YoutubeDL(ydl_opts)\ as\ ydl:
\ \ \ \ ydl.download([\[aq]https://www.youtube.com/watch?v=BaW_jenozKc\[aq]])
\f[]
.fi
.PP
Most likely, you\[aq]ll want to use various options.
For a list of options available, have a look at
\f[C]youtube_dl/YoutubeDL.py\f[] (https://github.com/ytdl-org/youtube-dl/blob/3e4cedf9e8cd3157df2457df7274d0c842421945/youtube_dl/YoutubeDL.py#L137-L312).
For a start, if you want to intercept youtube\-dl\[aq]s output, set a
\f[C]logger\f[] object.
.PP
Here\[aq]s a more complete example of a program that outputs only errors
(and a short message after the download is finished), and
downloads/converts the video to an mp3 file:
.IP
.nf
\f[C]
from\ __future__\ import\ unicode_literals
import\ youtube_dl


class\ MyLogger(object):
\ \ \ \ def\ debug(self,\ msg):
\ \ \ \ \ \ \ \ pass

\ \ \ \ def\ warning(self,\ msg):
\ \ \ \ \ \ \ \ pass

\ \ \ \ def\ error(self,\ msg):
\ \ \ \ \ \ \ \ print(msg)


def\ my_hook(d):
\ \ \ \ if\ d[\[aq]status\[aq]]\ ==\ \[aq]finished\[aq]:
\ \ \ \ \ \ \ \ print(\[aq]Done\ downloading,\ now\ converting\ ...\[aq])


ydl_opts\ =\ {
\ \ \ \ \[aq]format\[aq]:\ \[aq]bestaudio/best\[aq],
\ \ \ \ \[aq]postprocessors\[aq]:\ [{
\ \ \ \ \ \ \ \ \[aq]key\[aq]:\ \[aq]FFmpegExtractAudio\[aq],
\ \ \ \ \ \ \ \ \[aq]preferredcodec\[aq]:\ \[aq]mp3\[aq],
\ \ \ \ \ \ \ \ \[aq]preferredquality\[aq]:\ \[aq]192\[aq],
\ \ \ \ }],
\ \ \ \ \[aq]logger\[aq]:\ MyLogger(),
\ \ \ \ \[aq]progress_hooks\[aq]:\ [my_hook],
}
with\ youtube_dl.YoutubeDL(ydl_opts)\ as\ ydl:
\ \ \ \ ydl.download([\[aq]https://www.youtube.com/watch?v=BaW_jenozKc\[aq]])
\f[]
.fi
.SH BUGS
.PP
Bugs and suggestions should be reported at:
<https://github.com/ytdl-org/youtube-dl/issues>.
Unless you were prompted to or there is another pertinent reason (e.g.
GitHub fails to accept the bug report), please do not send bug reports
via personal email.
For discussions, join us in the IRC channel
#youtube\-dl (irc://chat.freenode.net/#youtube-dl) on freenode
(webchat (https://webchat.freenode.net/?randomnick=1&channels=youtube-dl)).
.PP
\f[B]Please include the full output of youtube\-dl when run with
\f[BC]\-v\f[B]\f[], i.e.
\f[B]add\f[] \f[C]\-v\f[] flag to \f[B]your command line\f[], copy the
\f[B]whole\f[] output and post it in the issue body wrapped in ``` for
better formatting.
It should look similar to this:
.IP
.nf
\f[C]
$\ youtube\-dl\ \-v\ <your\ command\ line>
[debug]\ System\ config:\ []
[debug]\ User\ config:\ []
[debug]\ Command\-line\ args:\ [u\[aq]\-v\[aq],\ u\[aq]https://www.youtube.com/watch?v=BaW_jenozKcj\[aq]]
[debug]\ Encodings:\ locale\ cp1251,\ fs\ mbcs,\ out\ cp866,\ pref\ cp1251
[debug]\ youtube\-dl\ version\ 2015.12.06
[debug]\ Git\ HEAD:\ 135392e
[debug]\ Python\ version\ 2.6.6\ \-\ Windows\-2003Server\-5.2.3790\-SP2
[debug]\ exe\ versions:\ ffmpeg\ N\-75573\-g1d0487f,\ ffprobe\ N\-75573\-g1d0487f,\ rtmpdump\ 2.4
[debug]\ Proxy\ map:\ {}
\&...
\f[]
.fi
.PP
\f[B]Do not post screenshots of verbose logs; only plain text is
acceptable.\f[]
.PP
The output (including the first lines) contains important debugging
information.
Issues without the full output are often not reproducible and therefore
do not get solved in short order, if ever.
.PP
Please re\-read your issue once again to avoid a couple of common
mistakes (you can and should use this as a checklist):
.SS Is the description of the issue itself sufficient?
.PP
We often get issue reports that we cannot really decipher.
While in most cases we eventually get the required information after
asking back multiple times, this poses an unnecessary drain on our
resources.
Many contributors, including myself, are also not native speakers, so we
may misread some parts.
.PP
So please elaborate on what feature you are requesting, or what bug you
want to be fixed.
Make sure that it\[aq]s obvious
.IP \[bu] 2
What the problem is
.IP \[bu] 2
How it could be fixed
.IP \[bu] 2
How your proposed solution would look like
.PP
If your report is shorter than two lines, it is almost certainly missing
some of these, which makes it hard for us to respond to it.
We\[aq]re often too polite to close the issue outright, but the missing
info makes misinterpretation likely.
As a committer myself, I often get frustrated by these issues, since the
only possible way for me to move forward on them is to ask for
clarification over and over.
.PP
For bug reports, this means that your report should contain the
\f[I]complete\f[] output of youtube\-dl when called with the
\f[C]\-v\f[] flag.
The error message you get for (most) bugs even says so, but you would
not believe how many of our bug reports do not contain this information.
.PP
If your server has multiple IPs or you suspect censorship, adding
\f[C]\-\-call\-home\f[] may be a good idea to get more diagnostics.
If the error is \f[C]ERROR:\ Unable\ to\ extract\ ...\f[] and you cannot
reproduce it from multiple countries, add \f[C]\-\-dump\-pages\f[]
(warning: this will yield a rather large output, redirect it to the file
\f[C]log.txt\f[] by adding \f[C]>log.txt\ 2>&1\f[] to your
command\-line) or upload the \f[C]\&.dump\f[] files you get when you add
\f[C]\-\-write\-pages\f[] somewhere (https://gist.github.com/).
.PP
\f[B]Site support requests must contain an example URL\f[].
An example URL is a URL you might want to download, like
\f[C]https://www.youtube.com/watch?v=BaW_jenozKc\f[].
There should be an obvious video present.
Except under very special circumstances, the main page of a video
service (e.g.
\f[C]https://www.youtube.com/\f[]) is \f[I]not\f[] an example URL.
.SS Are you using the latest version?
.PP
Before reporting any issue, type \f[C]youtube\-dl\ \-U\f[].
This should report that you\[aq]re up\-to\-date.
About 20% of the reports we receive are already fixed, but people are
using outdated versions.
This goes for feature requests as well.
.SS Is the issue already documented?
.PP
Make sure that someone has not already opened the issue you\[aq]re
trying to open.
Search at the top of the window or browse the GitHub
Issues (https://github.com/ytdl-org/youtube-dl/search?type=Issues) of
this repository.
If there is an issue, feel free to write something along the lines of
"This affects me as well, with version 2015.01.01.
Here is some more information on the issue: ...".
While some issues may be old, a new post into them often spurs rapid
activity.
.SS Why are existing options not enough?
.PP
Before requesting a new feature, please have a quick peek at the list of
supported
options (https://github.com/ytdl-org/youtube-dl/blob/master/README.md#options).
Many feature requests are for features that actually exist already!
Please, absolutely do show off your work in the issue report and detail
how the existing similar options do \f[I]not\f[] solve your problem.
.SS Is there enough context in your bug report?
.PP
People want to solve problems, and often think they do us a favor by
breaking down their larger problems (e.g.
wanting to skip already downloaded files) to a specific request (e.g.
requesting us to look whether the file exists before downloading the
info page).
However, what often happens is that they break down the problem into two
steps: One simple, and one impossible (or extremely complicated one).
.PP
We are then presented with a very complicated request when the original
problem could be solved far easier, e.g.
by recording the downloaded video IDs in a separate file.
To avoid this, you must include the greater context where it is
non\-obvious.
In particular, every feature request that does not consist of adding
support for a new site should contain a use case scenario that explains
in what situation the missing feature would be useful.
.SS Does the issue involve one problem, and one problem only?
.PP
Some of our users seem to think there is a limit of issues they can or
should open.
There is no limit of issues they can or should open.
While it may seem appealing to be able to dump all your issues into one
ticket, that means that someone who solves one of your issues cannot
mark the issue as closed.
Typically, reporting a bunch of issues leads to the ticket lingering
since nobody wants to attack that behemoth, until someone mercifully
splits the issue into multiple ones.
.PP
In particular, every site support request issue should only pertain to
services at one site (generally under a common domain, but always using
the same backend technology).
Do not request support for vimeo user videos, White house podcasts, and
Google Plus pages in the same issue.
Also, make sure that you don\[aq]t post bug reports alongside feature
requests.
As a rule of thumb, a feature request does not include outputs of
youtube\-dl that are not immediately related to the feature at hand.
Do not post reports of a network error alongside the request for a new
video service.
.SS Is anyone going to need the feature?
.PP
Only post features that you (or an incapacitated friend you can
personally talk to) require.
Do not post features because they seem like a good idea.
If they are really useful, they will be requested by someone who
requires them.
.SS Is your question about youtube\-dl?
.PP
It may sound strange, but some bug reports we receive are completely
unrelated to youtube\-dl and relate to a different, or even the
reporter\[aq]s own, application.
Please make sure that you are actually using youtube\-dl.
If you are using a UI for youtube\-dl, report the bug to the maintainer
of the actual application providing the UI.
On the other hand, if your UI for youtube\-dl fails in some way you
believe is related to youtube\-dl, by all means, go ahead and report the
bug.
.SH COPYRIGHT
.PP
youtube\-dl is released into the public domain by the copyright holders.
.PP
This README file was originally written by Daniel
Bolton (https://github.com/dbbolton) and is likewise released into the
public domain.
